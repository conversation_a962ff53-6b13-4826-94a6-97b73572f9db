/* 公共样式 - 所有页面共用 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f5f0;
}

a {
    text-decoration: none;
    color: #8b2a2a;
    transition: color 0.3s ease;
}

a:hover {
    color: #d44949;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #8b2a2a;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #d44949;
}

.btn-big {
    padding: 10px 20px;
    font-size: 1.1em;
}

/* 头部样式 */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5%;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.8em;
    color: #8b2a2a;
    font-weight: bold;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    font-size: 1.1em;
    padding: 5px 10px;
    border-radius: 4px;
}

nav ul li a.active {
    background-color: #8b2a2a;
    color: #fff;
}

/* 页面横幅通用样式 */
.page-banner {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.page-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 20px;
    border-radius: 5px;
    width: 80%;
    max-width: 800px;
}

.page-title h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

/* 通用章节标题样式 */
section h2 {
    text-align: center;
    font-size: 2em;
    margin-bottom: 30px;
    color: #8b2a2a;
    position: relative;
}

section h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #8b2a2a;
    margin: 10px auto;
}

/* 通用卡片样式 */
.card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* 视频和音频容器通用样式 */
.video-container, .audio-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.video-container video, .audio-container audio {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.audio-description {
    color: #666;
    font-style: italic;
    margin-top: 15px;
}

/* 页脚样式 */
footer {
    background-color: #333;
    color: #fff;
    padding: 40px 0 0;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-section {
    flex: 1;
    min-width: 300px;
    margin-bottom: 30px;
}

.footer-section h3 {
    font-size: 1.3em;
    margin-bottom: 15px;
    position: relative;
}

.footer-section h3:after {
    content: '';
    display: block;
    width: 30px;
    height: 2px;
    background-color: #8b2a2a;
    margin-top: 5px;
}

.footer-section p {
    margin-bottom: 15px;
}

.contact span {
    display: block;
    margin-bottom: 10px;
}

.footer-section.links ul li {
    margin-bottom: 10px;
}

.text-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 4px;
}

textarea.text-input {
    height: 100px;
    resize: none;
}

.footer-bottom {
    background-color: #222;
    padding: 10px 0;
    text-align: center;
    font-size: 0.9em;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        padding: 10px;
    }
    
    .logo {
        margin-bottom: 10px;
    }
    
    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    nav ul li {
        margin: 5px;
    }
    
    .page-banner {
        height: 200px;
    }
    
    .page-title h1 {
        font-size: 1.8em;
    }
    
    .footer-content {
        flex-direction: column;
    }
    
    .footer-section {
        min-width: auto;
    }
}
