<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="300" viewBox="0 0 1200 300">
  <!-- 背景 -->
  <rect width="1200" height="300" fill="#f9eee2"/>
  
  <!-- 装饰边框 -->
  <rect x="10" y="10" width="1180" height="280" fill="none" stroke="#8b2a2a" stroke-width="2" rx="5" ry="5" stroke-dasharray="10,5"/>
  
  <!-- 中央图案：汉服分类展示 -->
  <g transform="translate(600, 150)">
    <!-- 中央圆形 -->
    <circle cx="0" cy="0" r="80" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="2"/>
    <text x="0" y="10" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#8b2a2a">汉服分类</text>
    
    <!-- 分类连接线和图标 -->
    <!-- 朝代分类 -->
    <g transform="translate(-300, -50)">
      <line x1="80" y1="50" x2="220" y2="50" stroke="#8b2a2a" stroke-width="2" stroke-dasharray="5,5"/>
      <circle cx="0" cy="0" r="60" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="2"/>
      <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#8b2a2a">朝代分类</text>
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">汉、唐、宋、明</text>
    </g>
    
    <!-- 性别分类 -->
    <g transform="translate(300, -50)">
      <line x1="-80" y1="50" x2="-220" y2="50" stroke="#8b2a2a" stroke-width="2" stroke-dasharray="5,5"/>
      <circle cx="0" cy="0" r="60" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="2"/>
      <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#8b2a2a">性别分类</text>
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">男装、女装</text>
    </g>
    
    <!-- 场合分类 -->
    <g transform="translate(-300, 100)">
      <line x1="80" y1="-50" x2="220" y2="-50" stroke="#8b2a2a" stroke-width="2" stroke-dasharray="5,5"/>
      <circle cx="0" cy="0" r="60" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="2"/>
      <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#8b2a2a">场合分类</text>
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">日常、礼服</text>
    </g>
    
    <!-- 结构分类 -->
    <g transform="translate(300, 100)">
      <line x1="-80" y1="-50" x2="-220" y2="-50" stroke="#8b2a2a" stroke-width="2" stroke-dasharray="5,5"/>
      <circle cx="0" cy="0" r="60" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="2"/>
      <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#8b2a2a">结构分类</text>
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">上衣下裳、连体</text>
    </g>
  </g>
  
  <!-- 标题 -->
  <g transform="translate(600, 50)">
    <rect x="-200" y="-30" width="400" height="60" fill="rgba(139, 42, 42, 0.8)" rx="10" ry="10"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="#fff">汉服分类体系</text>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.2">
    <!-- 左上角装饰 -->
    <g transform="translate(50, 50)">
      <path d="M0 0 C10 10, 20 10, 30 0 C40 10, 50 10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右上角装饰 -->
    <g transform="translate(1150, 50)">
      <path d="M0 0 C-10 10, -20 10, -30 0 C-40 10, -50 10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 左下角装饰 -->
    <g transform="translate(50, 250)">
      <path d="M0 0 C10 -10, 20 -10, 30 0 C40 -10, 50 -10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右下角装饰 -->
    <g transform="translate(1150, 250)">
      <path d="M0 0 C-10 -10, -20 -10, -30 0 C-40 -10, -50 -10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
  </g>
</svg>