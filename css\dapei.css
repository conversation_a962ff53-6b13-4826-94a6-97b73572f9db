/* 搭配页面样式 - 包含公共样式和搭配页面专用样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f5f0;
}

a {
    text-decoration: none;
    color: #8b2a2a;
    transition: color 0.3s ease;
}

a:hover {
    color: #d44949;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #8b2a2a;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #d44949;
}

.btn-big {
    padding: 10px 20px;
    font-size: 1.1em;
}

/* 头部样式 */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5%;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.8em;
    color: #8b2a2a;
    font-weight: bold;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    font-size: 1.1em;
    padding: 5px 10px;
    border-radius: 4px;
}

nav ul li a.active {
    background-color: #8b2a2a;
    color: #fff;
}

/* 页面横幅通用样式 */
.page-banner {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.page-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 20px;
    border-radius: 5px;
    width: 80%;
    max-width: 800px;
}

.page-title h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

/* 通用章节标题样式 */
section h2 {
    text-align: center;
    font-size: 2em;
    margin-bottom: 30px;
    color: #8b2a2a;
    position: relative;
}

section h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #8b2a2a;
    margin: 10px auto;
}

/* 通用卡片样式 */
.card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* 视频和音频容器通用样式 */
.video-container, .audio-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.video-container video, .audio-container audio {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.audio-description {
    color: #666;
    font-style: italic;
    margin-top: 15px;
}

/* 页脚样式 */
footer {
    background-color: #333;
    color: #fff;
    padding: 40px 0 0;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-section {
    flex: 1;
    min-width: 300px;
    margin-bottom: 30px;
}

.footer-section h3 {
    font-size: 1.3em;
    margin-bottom: 15px;
    position: relative;
}

.footer-section h3:after {
    content: '';
    display: block;
    width: 30px;
    height: 2px;
    background-color: #8b2a2a;
    margin-top: 5px;
}

.footer-section p {
    margin-bottom: 15px;
}

.contact span {
    display: block;
    margin-bottom: 10px;
}

.footer-section.links ul li {
    margin-bottom: 10px;
}

.text-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 4px;
}

textarea.text-input {
    height: 100px;
    resize: none;
}

.footer-bottom {
    background-color: #222;
    padding: 10px 0;
    text-align: center;
    font-size: 0.9em;
}



/* 搭配页面专用样式 */


/* 搭配介绍部分 */
.matching-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.matching-intro p {
    line-height: 1.8;
    color: #555;
    font-size: 1.1em;
}

/* 色彩搭配部分 */
.color-matching {
    margin: 50px 0;
}

.color-theory {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 30px;
    margin-bottom: 40px;
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.color-text {
    flex: 2;
    min-width: 300px;
}

.color-text h3 {
    color: #8b2a2a;
    margin-bottom: 15px;
    font-size: 1.5em;
}

.color-text p {
    margin-bottom: 15px;
    line-height: 1.7;
    text-align: justify;
    color: #555;
}

.color-wheel {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.color-wheel img {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 色彩卡片 */
.color-examples h3 {
    text-align: center;
    margin-bottom: 30px;
    color: #8b2a2a;
    font-size: 1.6em;
}

.color-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}

.color-card {
    width: 250px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.color-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.color-sample {
    height: 40px;
    width: 50%;
    float: left;
}

.color-card h4 {
    text-align: center;
    margin: 15px 0 10px;
    color: #8b2a2a;
    clear: both;
    padding-top: 10px;
}

.color-card p {
    padding: 0 15px 20px;
    text-align: center;
    color: #666;
    line-height: 1.6;
    font-size: 0.95em;
}

/* 配饰搭配部分 */
.accessory-matching {
    margin: 50px 0;
}

.accessory-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.accessory-intro p {
    line-height: 1.8;
    color: #555;
    font-size: 1.1em;
}

.accessory-types {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.accessory-type {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.accessory-type:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accessory-type img {
    width: 300px;
    height: 300px;
    object-fit: cover;
    flex-shrink: 0;
}

.accessory-info {
    flex: 1;
    padding: 25px;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.accessory-info h3 {
    font-size: 1.6em;
    margin-bottom: 15px;
    color: #8b2a2a;
    border-bottom: 2px solid #8b2a2a;
    padding-bottom: 5px;
}

.accessory-info p {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #555;
    text-align: justify;
}

.accessory-info ul {
    margin-left: 0;
    list-style: none;
}

.accessory-info ul li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
    color: #666;
    line-height: 1.6;
}

.accessory-info ul li:before {
    content: "•";
    color: #8b2a2a;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.accessory-info ul li strong {
    color: #8b2a2a;
}

/* 场合搭配部分 */
.occasion-matching {
    margin: 50px 0;
}

.occasion-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.occasion-intro p {
    line-height: 1.8;
    color: #555;
    font-size: 1.1em;
}

.occasion-types {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.occasion-type {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.occasion-type:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.occasion-type img {
    width: 300px;
    height: 300px;
    object-fit: cover;
    flex-shrink: 0;
}

.occasion-info {
    flex: 1;
    padding: 25px;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.occasion-info h3 {
    font-size: 1.6em;
    margin-bottom: 15px;
    color: #8b2a2a;
    border-bottom: 2px solid #8b2a2a;
    padding-bottom: 5px;
}

.occasion-info p {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #555;
    text-align: justify;
}

.occasion-info ul {
    margin-left: 0;
    list-style: none;
}

.occasion-info ul li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
    color: #666;
    line-height: 1.6;
}

.occasion-info ul li:before {
    content: "•";
    color: #8b2a2a;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.occasion-info ul li strong {
    color: #8b2a2a;
}

/* 搭配技巧部分 */
.matching-tips {
    margin: 50px 0;
}

.tips-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}

.tip-card {
    width: 300px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 25px;
    text-align: center;
    transition: transform 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tip-icon {
    width: 50px;
    height: 50px;
    background-color: #8b2a2a;
    color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 20px;
    font-size: 1.5em;
    font-weight: bold;
}

.tip-card h3 {
    margin-bottom: 15px;
    color: #8b2a2a;
    font-size: 1.3em;
}

.tip-card p {
    color: #666;
    line-height: 1.6;
}

/* 搭配案例展示 */
.matching-gallery {
    margin: 50px 0;
}

.gallery-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}

.gallery-item {
    width: 300px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.gallery-caption {
    padding: 20px;
}

.gallery-caption h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
    font-size: 1.2em;
}

.gallery-caption p {
    color: #666;
    line-height: 1.6;
    font-size: 0.95em;
}

/* 视频部分样式 */
.video-section {
    margin: 50px 0;
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.video-description {
    text-align: center;
    margin-top: 15px;
    color: #666;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .color-theory {
        flex-direction: column;
        padding: 20px;
    }

    .color-text {
        min-width: auto;
    }

    .color-wheel {
        min-width: auto;
    }

    .color-cards {
        flex-direction: column;
        align-items: center;
    }

    .color-card {
        width: 100%;
        max-width: 350px;
    }

    .accessory-type, .occasion-type {
        flex-direction: column;
    }

    .accessory-type img, .occasion-type img {
        width: 100%;
        height: 250px;
    }

    .accessory-info, .occasion-info {
        min-width: auto;
        padding: 20px;
    }

    .tips-container {
        flex-direction: column;
        align-items: center;
    }

    .tip-card {
        width: 100%;
        max-width: 400px;
    }

    .gallery-container {
        flex-direction: column;
        align-items: center;
    }

    .gallery-item {
        width: 100%;
        max-width: 400px;
    }
}

@media (max-width: 480px) {
    .matching-intro, .accessory-intro, .occasion-intro {
        margin: 0 10px 30px;
        padding: 15px;
    }

    .color-theory {
        padding: 15px;
    }

    .accessory-info h3, .occasion-info h3 {
        font-size: 1.3em;
    }

    .tip-card {
        padding: 20px;
    }

    .tip-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2em;
    }
}
