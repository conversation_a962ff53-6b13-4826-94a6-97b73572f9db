<svg xmlns="http://www.w3.org/2000/svg" width="800" height="450" viewBox="0 0 800 450">
  <!-- 背景 -->
  <rect width="800" height="450" fill="#f9f5f0" rx="10" ry="10"/>
  
  <!-- 视频播放器边框 -->
  <rect x="10" y="10" width="780" height="430" fill="none" stroke="#8b2a2a" stroke-width="2" rx="8" ry="8"/>
  
  <!-- 视频区域 -->
  <rect x="20" y="20" width="760" height="410" fill="rgba(139, 42, 42, 0.1)" rx="5" ry="5"/>
  
  <!-- 播放按钮 -->
  <g transform="translate(400, 225)">
    <circle cx="0" cy="0" r="60" fill="rgba(255, 255, 255, 0.7)" stroke="#8b2a2a" stroke-width="2"/>
    <polygon points="-25,-40 -25,40 40,0" fill="#8b2a2a"/>
  </g>
  
  <!-- 视频标题 -->
  <g transform="translate(400, 60)">
    <rect x="-300" y="-25" width="600" height="50" fill="rgba(139, 42, 42, 0.8)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#fff">汉服文化传承与发展</text>
  </g>
  
  <!-- 视频内容示意 -->
  <g transform="translate(400, 225)" opacity="0.3">
    <!-- 简化的汉服人物轮廓 -->
    <g transform="translate(-150, 0)">
      <path d="M0 -70 C-15 -70, -30 -60, -30 -50 L-30 0 L-40 40 L-30 60 L-15 80 L0 90 L15 80 L30 60 L40 40 L30 0 L30 -50 C30 -60, 15 -70, 0 -70 Z" fill="#8b2a2a"/>
      <path d="M0 -70 C-5 -80, -2 -85, 0 -90 C2 -85, 5 -80, 0 -70 Z" fill="#8b2a2a"/>
    </g>
    
    <g transform="translate(0, 0)">
      <path d="M0 -70 C-15 -70, -30 -60, -30 -50 L-30 0 L-40 40 L-30 60 L-15 80 L0 90 L15 80 L30 60 L40 40 L30 0 L30 -50 C30 -60, 15 -70, 0 -70 Z" fill="#8b2a2a"/>
      <path d="M0 -70 C-5 -80, -2 -85, 0 -90 C2 -85, 5 -80, 0 -70 Z" fill="#8b2a2a"/>
    </g>
    
    <g transform="translate(150, 0)">
      <path d="M0 -70 C-15 -70, -30 -60, -30 -50 L-30 0 L-40 40 L-30 60 L-15 80 L0 90 L15 80 L30 60 L40 40 L30 0 L30 -50 C30 -60, 15 -70, 0 -70 Z" fill="#8b2a2a"/>
      <path d="M0 -70 C-5 -80, -2 -85, 0 -90 C2 -85, 5 -80, 0 -70 Z" fill="#8b2a2a"/>
    </g>
    
    <!-- 简化的建筑轮廓 -->
    <g transform="translate(0, -100)">
      <path d="M-200 50 L-150 0 L150 0 L200 50" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M-120 50 L-120 120 L-100 120 L-100 50" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M-60 50 L-60 120 L-40 120 L-40 50" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M40 50 L40 120 L60 120 L60 50" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M100 50 L100 120 L120 120 L120 50" fill="none" stroke="#8b2a2a" stroke-width="2"/>
    </g>
  </g>
  
  <!-- 视频时长 -->
  <g transform="translate(750, 400)">
    <rect x="-50" y="-15" width="80" height="30" fill="rgba(0, 0, 0, 0.5)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#fff">10:30</text>
  </g>
  
  <!-- 视频来源 -->
  <g transform="translate(100, 400)">
    <rect x="-80" y="-15" width="160" height="30" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#fff">汉服文化研究会</text>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.2">
    <!-- 左上角装饰 -->
    <g transform="translate(50, 50)">
      <path d="M0 0 C10 10, 20 10, 30 0 C40 10, 50 10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右上角装饰 -->
    <g transform="translate(750, 50)">
      <path d="M0 0 C-10 10, -20 10, -30 0 C-40 10, -50 10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 左下角装饰 -->
    <g transform="translate(50, 400)">
      <path d="M0 0 C10 -10, 20 -10, 30 0 C40 -10, 50 -10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右下角装饰 -->
    <g transform="translate(750, 400)">
      <path d="M0 0 C-10 -10, -20 -10, -30 0 C-40 -10, -50 -10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
  </g>
</svg>