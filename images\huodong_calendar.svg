<svg xmlns="http://www.w3.org/2000/svg" width="800" height="500" viewBox="0 0 800 500">
  <!-- 背景 -->
  <rect width="800" height="500" fill="#f9f5f0" rx="10" ry="10"/>
  
  <!-- 装饰边框 -->
  <rect x="10" y="10" width="780" height="480" fill="none" stroke="#8b2a2a" stroke-width="2" rx="8" ry="8" stroke-dasharray="5,5"/>
  
  <!-- 标题 -->
  <g transform="translate(400, 50)">
    <rect x="-150" y="-25" width="300" height="50" fill="rgba(139, 42, 42, 0.8)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#fff">汉服活动日历</text>
  </g>
  
  <!-- 日历框架 -->
  <g transform="translate(400, 250)">
    <!-- 日历外框 -->
    <rect x="-250" y="-150" width="500" height="300" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="2" rx="5" ry="5"/>
    
    <!-- 日历标题栏 -->
    <rect x="-250" y="-150" width="500" height="40" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <text x="0" y="-130" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">2023年 活动安排</text>
    
    <!-- 星期标题 -->
    <g transform="translate(-215, -100)">
      <text font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">周日</text>
    </g>
    <g transform="translate(-145, -100)">
      <text font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">周一</text>
    </g>
    <g transform="translate(-75, -100)">
      <text font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">周二</text>
    </g>
    <g transform="translate(-5, -100)">
      <text font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">周三</text>
    </g>
    <g transform="translate(65, -100)">
      <text font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">周四</text>
    </g>
    <g transform="translate(135, -100)">
      <text font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">周五</text>
    </g>
    <g transform="translate(205, -100)">
      <text font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">周六</text>
    </g>
    
    <!-- 日历网格线 -->
    <!-- 横线 -->
    <line x1="-250" y1="-80" x2="250" y2="-80" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    <line x1="-250" y1="-30" x2="250" y2="-30" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    <line x1="-250" y1="20" x2="250" y2="20" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    <line x1="-250" y1="70" x2="250" y2="70" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    <line x1="-250" y1="120" x2="250" y2="120" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    
    <!-- 竖线 -->
    <line x1="-180" y1="-110" x2="-180" y2="150" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    <line x1="-110" y1="-110" x2="-110" y2="150" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    <line x1="-40" y1="-110" x2="-40" y2="150" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    <line x1="30" y1="-110" x2="30" y2="150" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    <line x1="100" y1="-110" x2="100" y2="150" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    <line x1="170" y1="-110" x2="170" y2="150" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="2,2"/>
    
    <!-- 活动标记 -->
    <!-- 传统节日活动 -->
    <g transform="translate(-215, -55)">
      <circle cx="0" cy="0" r="15" fill="rgba(212, 73, 73, 0.6)"/>
      <text x="0" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#fff">1</text>
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" text-anchor="middle" fill="#8b2a2a">元旦</text>
    </g>
    
    <g transform="translate(65, -5)">
      <circle cx="0" cy="0" r="15" fill="rgba(212, 73, 73, 0.6)"/>
      <text x="0" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#fff">5</text>
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" text-anchor="middle" fill="#8b2a2a">元宵</text>
    </g>
    
    <g transform="translate(-75, 45)">
      <circle cx="0" cy="0" r="15" fill="rgba(212, 73, 73, 0.6)"/>
      <text x="0" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#fff">9</text>
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" text-anchor="middle" fill="#8b2a2a">端午</text>
    </g>
    
    <g transform="translate(205, 95)">
      <circle cx="0" cy="0" r="15" fill="rgba(212, 73, 73, 0.6)"/>
      <text x="0" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#fff">15</text>
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" text-anchor="middle" fill="#8b2a2a">中秋</text>
    </g>
    
    <!-- 汉服雅集 -->
    <g transform="translate(-5, -55)">
      <rect x="-15" y="-15" width="30" height="30" fill="rgba(74, 111, 165, 0.6)" rx="5" ry="5"/>
      <text x="0" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#fff">3</text>
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" text-anchor="middle" fill="#8b2a2a">雅集</text>
    </g>
    
    <g transform="translate(135, 45)">
      <rect x="-15" y="-15" width="30" height="30" fill="rgba(74, 111, 165, 0.6)" rx="5" ry="5"/>
      <text x="0" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#fff">12</text>
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" text-anchor="middle" fill="#8b2a2a">雅集</text>
    </g>
    
    <!-- 汉服摄影 -->
    <g transform="translate(-145, 95)">
      <polygon points="0,-15 15,0 0,15 -15,0" fill="rgba(230, 180, 34, 0.6)"/>
      <text x="0" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#fff">17</text>
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" text-anchor="middle" fill="#8b2a2a">摄影</text>
    </g>
    
    <g transform="translate(65, 95)">
      <polygon points="0,-15 15,0 0,15 -15,0" fill="rgba(230, 180, 34, 0.6)"/>
      <text x="0" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#fff">20</text>
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" text-anchor="middle" fill="#8b2a2a">摄影</text>
    </g>
  </g>
  
  <!-- 图例 -->
  <g transform="translate(150, 430)">
    <rect x="-100" y="-20" width="200" height="40" fill="rgba(139, 42, 42, 0.1)" rx="5" ry="5"/>
    
    <!-- 传统节日 -->
    <g transform="translate(-60, 0)">
      <circle cx="0" cy="0" r="10" fill="rgba(212, 73, 73, 0.6)"/>
      <text x="20" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="start" fill="#8b2a2a">传统节日</text>
    </g>
    
    <!-- 汉服雅集 -->
    <g transform="translate(60, 0)">
      <rect x="-10" y="-10" width="20" height="20" fill="rgba(74, 111, 165, 0.6)" rx="3" ry="3"/>
      <text x="20" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="start" fill="#8b2a2a">汉服雅集</text>
    </g>
  </g>
  
  <g transform="translate(550, 430)">
    <rect x="-100" y="-20" width="200" height="40" fill="rgba(139, 42, 42, 0.1)" rx="5" ry="5"/>
    
    <!-- 汉服摄影 -->
    <g transform="translate(0, 0)">
      <polygon points="0,-10 10,0 0,10 -10,0" fill="rgba(230, 180, 34, 0.6)"/>
      <text x="20" y="4" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="start" fill="#8b2a2a">汉服摄影</text>
    </g>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.2">
    <!-- 左上角装饰 -->
    <g transform="translate(50, 50)">
      <path d="M0 0 C10 10, 20 10, 30 0 C40 10, 50 10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右上角装饰 -->
    <g transform="translate(750, 50)">
      <path d="M0 0 C-10 10, -20 10, -30 0 C-40 10, -50 10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 左下角装饰 -->
    <g transform="translate(50, 450)">
      <path d="M0 0 C10 -10, 20 -10, 30 0 C40 -10, 50 -10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右下角装饰 -->
    <g transform="translate(750, 450)">
      <path d="M0 0 C-10 -10, -20 -10, -30 0 C-40 -10, -50 -10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
  </g>
</svg>