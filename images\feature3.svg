<svg xmlns="http://www.w3.org/2000/svg" width="400" height="250" viewBox="0 0 400 250">
  <!-- 背景 -->
  <rect width="400" height="250" fill="#f5e8d6" rx="5" ry="5"/>
  
  <!-- 装饰边框 -->
  <rect x="5" y="5" width="390" height="240" fill="none" stroke="#8b2a2a" stroke-width="1" rx="3" ry="3"/>
  
  <!-- 中央图案：文化元素组合 -->
  <g transform="translate(200, 125)">
    <!-- 中央图案：太极 -->
    <circle cx="0" cy="0" r="50" fill="none" stroke="#8b2a2a" stroke-width="2"/>
    <path d="M0 -50 A50 50 0 0 1 0 50 A25 25 0 0 1 0 0 A25 25 0 0 0 0 -50" fill="#8b2a2a"/>
    <circle cx="0" cy="-25" r="5" fill="#f5e8d6"/>
    <circle cx="0" cy="25" r="5" fill="#8b2a2a"/>
    
    <!-- 周围文化元素：琴 -->
    <g transform="translate(-80, -40)">
      <rect x="-20" y="-5" width="40" height="10" fill="#8b2a2a" rx="2" ry="2"/>
      <line x1="-15" y1="-5" x2="-15" y2="5" stroke="#f5e8d6" stroke-width="0.5"/>
      <line x1="-5" y1="-5" x2="-5" y2="5" stroke="#f5e8d6" stroke-width="0.5"/>
      <line x1="5" y1="-5" x2="5" y2="5" stroke="#f5e8d6" stroke-width="0.5"/>
      <line x1="15" y1="-5" x2="15" y2="5" stroke="#f5e8d6" stroke-width="0.5"/>
    </g>
    
    <!-- 周围文化元素：棋 -->
    <g transform="translate(80, -40)">
      <rect x="-15" y="-15" width="30" height="30" fill="none" stroke="#8b2a2a" stroke-width="1"/>
      <line x1="-15" y1="-5" x2="15" y2="-5" stroke="#8b2a2a" stroke-width="0.5"/>
      <line x1="-15" y1="5" x2="15" y2="5" stroke="#8b2a2a" stroke-width="0.5"/>
      <line x1="-5" y1="-15" x2="-5" y2="15" stroke="#8b2a2a" stroke-width="0.5"/>
      <line x1="5" y1="-15" x2="5" y2="15" stroke="#8b2a2a" stroke-width="0.5"/>
      <circle cx="-5" cy="-5" r="2" fill="#8b2a2a"/>
      <circle cx="5" cy="5" r="2" fill="#8b2a2a"/>
    </g>
    
    <!-- 周围文化元素：书 -->
    <g transform="translate(-80, 40)">
      <rect x="-15" y="-10" width="30" height="20" fill="none" stroke="#8b2a2a" stroke-width="1"/>
      <line x1="0" y1="-10" x2="0" y2="10" stroke="#8b2a2a" stroke-width="0.5"/>
      <line x1="-10" y1="-5" x2="-5" y2="-5" stroke="#8b2a2a" stroke-width="0.5"/>
      <line x1="-10" y1="0" x2="-5" y2="0" stroke="#8b2a2a" stroke-width="0.5"/>
      <line x1="-10" y1="5" x2="-5" y2="5" stroke="#8b2a2a" stroke-width="0.5"/>
      <line x1="5" y1="-5" x2="10" y2="-5" stroke="#8b2a2a" stroke-width="0.5"/>
      <line x1="5" y1="0" x2="10" y2="0" stroke="#8b2a2a" stroke-width="0.5"/>
      <line x1="5" y1="5" x2="10" y2="5" stroke="#8b2a2a" stroke-width="0.5"/>
    </g>
    
    <!-- 周围文化元素：画 -->
    <g transform="translate(80, 40)">
      <rect x="-15" y="-20" width="30" height="40" fill="none" stroke="#8b2a2a" stroke-width="1"/>
      <path d="M-10 -10 C-5 -15, 5 -15, 10 -10 C15 -5, 15 5, 10 10 C5 15, -5 15, -10 10 C-15 5, -15 -5, -10 -10" fill="none" stroke="#8b2a2a" stroke-width="0.5"/>
    </g>
    
    <!-- 连接线 -->
    <line x1="-30" y1="0" x2="-50" y2="0" stroke="#8b2a2a" stroke-width="0.5" stroke-dasharray="2,2"/>
    <line x1="30" y1="0" x2="50" y2="0" stroke="#8b2a2a" stroke-width="0.5" stroke-dasharray="2,2"/>
    <line x1="0" y1="-30" x2="0" y2="-50" stroke="#8b2a2a" stroke-width="0.5" stroke-dasharray="2,2"/>
    <line x1="0" y1="30" x2="0" y2="50" stroke="#8b2a2a" stroke-width="0.5" stroke-dasharray="2,2"/>
  </g>
  
  <!-- 标题 -->
  <g transform="translate(200, 220)">
    <rect x="-100" y="-20" width="200" height="40" fill="rgba(139, 42, 42, 0.8)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">文化内涵</text>
  </g>
  
  <!-- 装饰花纹 -->
  <g fill="#8b2a2a" opacity="0.2">
    <path d="M20 20 C30 10, 40 10, 50 20 C60 10, 70 10, 80 20" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    <path d="M320 20 C330 10, 340 10, 350 20 C360 10, 370 10, 380 20" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    <path d="M20 230 C30 240, 40 240, 50 230 C60 240, 70 240, 80 230" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    <path d="M320 230 C330 240, 340 240, 350 230 C360 240, 370 240, 380 230" stroke="#8b2a2a" stroke-width="1" fill="none"/>
  </g>
</svg>