/* 购物页面专用样式 */

/* 购物介绍部分 */
.shopping-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.shopping-intro p {
    line-height: 1.8;
    color: #555;
    font-size: 1.1em;
}

/* 购物技巧部分 */
.shopping-tips {
    margin: 50px 0;
}

.tips-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}

.tip-card {
    width: 300px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 25px;
    text-align: center;
    transition: transform 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tip-icon {
    width: 50px;
    height: 50px;
    background-color: #8b2a2a;
    color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 20px;
    font-size: 1.5em;
    font-weight: bold;
}

.tip-card h3 {
    margin-bottom: 15px;
    color: #8b2a2a;
    font-size: 1.3em;
}

.tip-card p {
    color: #666;
    line-height: 1.6;
}

/* 品牌推荐部分 */
.brand-recommend {
    margin: 50px 0;
}

.brand-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.brand-card {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.brand-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.brand-card img {
    width: 200px;
    height: 200px;
    object-fit: contain;
    flex-shrink: 0;
    padding: 20px;
    background-color: #f9f9f9;
}

.brand-info {
    flex: 1;
    padding: 25px;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.brand-info h3 {
    font-size: 1.6em;
    margin-bottom: 15px;
    color: #8b2a2a;
    border-bottom: 2px solid #8b2a2a;
    padding-bottom: 5px;
}

.brand-info p {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #555;
    text-align: justify;
}

.brand-features {
    margin-bottom: 15px;
}

.brand-features span:first-child {
    color: #8b2a2a;
    font-weight: bold;
    margin-right: 10px;
}

.feature-tag {
    display: inline-block;
    background-color: #f0f0f0;
    color: #666;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    margin-right: 8px;
    margin-bottom: 5px;
}

.brand-price {
    margin-bottom: 20px;
}

.brand-price span:first-child {
    color: #8b2a2a;
    font-weight: bold;
    margin-right: 10px;
}

.price {
    color: #e74c3c;
    font-weight: bold;
    font-size: 1.1em;
}

/* 产品推荐部分 */
.product-recommend {
    margin: 50px 0;
}

.product-filter {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-filter span {
    color: #8b2a2a;
    font-weight: bold;
    margin-right: 15px;
}

/* 隐藏筛选单选按钮 */
.filter-radio {
    display: none;
}

.filter-btn {
    background-color: #f0f0f0;
    color: #666;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    margin: 0 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.filter-btn:hover {
    background-color: #e0e0e0;
}

/* 当对应的单选按钮被选中时，筛选按钮变为激活状态 */
.filter-radio:checked + .filter-btn {
    background-color: #8b2a2a;
    color: #fff;
}

/* 产品筛选显示逻辑 */
.product-card {
    display: block; /* 默认显示所有产品 */
}

/* 当选择特定筛选时，先隐藏所有产品 */
#filter-female:checked ~ .product-container .product-card,
#filter-male:checked ~ .product-container .product-card,
#filter-accessory:checked ~ .product-container .product-card {
    display: none;
}

/* 然后显示对应类别的产品 */
#filter-all:checked ~ .product-container .product-card {
    display: block;
}

/* 只显示女装 */
#filter-female:checked ~ .product-container .product-card[data-category="female"] {
    display: block;
}

/* 只显示男装 */
#filter-male:checked ~ .product-container .product-card[data-category="male"] {
    display: block;
}

/* 只显示配饰 */
#filter-accessory:checked ~ .product-container .product-card[data-category="accessory"] {
    display: block;
}

.product-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}

.product-card {
    width: 300px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 10px;
}

.product-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #e74c3c;
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.product-info {
    padding: 20px;
}

.product-info h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
    font-size: 1.2em;
}

.product-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
    font-size: 0.95em;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.product-price {
    color: #e74c3c;
    font-weight: bold;
    font-size: 1.2em;
}

.product-sales {
    color: #999;
    font-size: 0.9em;
}

/* 购物指南部分 */
.shopping-guide {
    margin: 50px 0;
}

.guide-container {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.guide-item {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.guide-item h3 {
    color: #8b2a2a;
    font-size: 1.6em;
    margin-bottom: 20px;
    border-bottom: 2px solid #8b2a2a;
    padding-bottom: 10px;
}

/* 尺寸表格样式 */
.size-guide table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.size-guide th,
.size-guide td {
    padding: 12px;
    text-align: center;
    border: 1px solid #ddd;
}

.size-guide th {
    background-color: #8b2a2a;
    color: #fff;
    font-weight: bold;
}

.size-guide tr:nth-child(even) {
    background-color: #f9f9f9;
}

.size-guide tr:hover {
    background-color: #f0f0f0;
}

.guide-tip {
    color: #666;
    font-style: italic;
    font-size: 0.9em;
    line-height: 1.6;
}

/* 材质指南样式 */
.material-guide {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.material-card {
    flex: 1;
    min-width: 250px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #8b2a2a;
}

.material-card h4 {
    color: #8b2a2a;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.material-card p {
    margin-bottom: 10px;
    color: #666;
    line-height: 1.6;
    font-size: 0.95em;
}

/* 购物平台部分 */
.shopping-platform {
    margin: 50px 0;
}

.platform-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}

.platform-card {
    width: 250px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    text-align: center;
    transition: transform 0.3s ease;
}

.platform-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.platform-card img {
    width: 100%;
    height: 150px;
    object-fit: contain;
    padding: 20px;
    background-color: #f9f9f9;
}

.platform-card h3 {
    padding: 15px 15px 10px;
    color: #8b2a2a;
    font-size: 1.2em;
}

.platform-card p {
    padding: 0 15px 15px;
    color: #666;
    line-height: 1.6;
    font-size: 0.95em;
}

.platform-card .btn {
    margin: 0 15px 20px;
    display: inline-block;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tips-container {
        flex-direction: column;
        align-items: center;
    }

    .tip-card {
        width: 100%;
        max-width: 400px;
    }

    .brand-card {
        flex-direction: column;
    }

    .brand-card img {
        width: 100%;
        height: 200px;
    }

    .brand-info {
        min-width: auto;
        padding: 20px;
    }

    .product-container {
        flex-direction: column;
        align-items: center;
    }

    .product-card {
        width: 100%;
        max-width: 400px;
    }

    .material-guide {
        flex-direction: column;
    }

    .material-card {
        min-width: auto;
    }

    .platform-container {
        flex-direction: column;
        align-items: center;
    }

    .platform-card {
        width: 100%;
        max-width: 350px;
    }

    .size-guide {
        overflow-x: auto;
    }

    .size-guide table {
        min-width: 500px;
    }
}

@media (max-width: 480px) {
    .shopping-intro {
        margin: 0 10px 30px;
        padding: 15px;
    }

    .guide-item {
        padding: 20px;
    }

    .tip-card {
        padding: 20px;
    }

    .tip-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2em;
    }

    .brand-info h3 {
        font-size: 1.3em;
    }

    .product-filter {
        padding: 15px;
    }

    .filter-btn {
        margin: 2px;
        padding: 6px 12px;
        font-size: 0.9em;
    }
}
