<svg width="1200" height="300" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f8e3e3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5d6c6;stop-opacity:1" />
    </linearGradient>
    <pattern id="pattern-cloth" width="30" height="30" patternUnits="userSpaceOnUse" patternTransform="rotate(45)">
      <rect width="30" height="30" fill="url(#bg-gradient)" />
      <line x1="0" y1="0" x2="30" y2="0" stroke="#e8c4b0" stroke-width="1" />
      <line x1="0" y1="15" x2="30" y2="15" stroke="#e8c4b0" stroke-width="0.5" />
    </pattern>
  </defs>
  
  <!-- 主背景 -->
  <rect width="1200" height="300" fill="url(#pattern-cloth)" />
  
  <!-- 装饰边框 -->
  <rect x="10" y="10" width="1180" height="280" fill="none" stroke="#a67c52" stroke-width="3" stroke-dasharray="5,5" />
  
  <!-- 左侧装饰图案：传统店铺 -->
  <g transform="translate(100, 150)">
    <!-- 店铺屋顶 -->
    <path d="M0,50 L100,0 L200,50 L180,50 L180,120 L20,120 L20,50 Z" fill="#8c4a3d" />
    <path d="M20,50 L180,50 L180,120 L20,120 Z" fill="#d9b99b" />
    
    <!-- 店铺门面 -->
    <rect x="40" y="70" width="50" height="50" fill="#5d4037" />
    <rect x="110" y="70" width="50" height="50" fill="#5d4037" />
    
    <!-- 店铺招牌 -->
    <rect x="60" y="55" width="80" height="15" fill="#8d6e63" />
    <text x="100" y="67" font-family="'SimSun', serif" font-size="12" fill="#f5f5f5" text-anchor="middle">汉服精品</text>
    
    <!-- 店铺装饰 -->
    <circle cx="65" cy="95" r="5" fill="#ffcc80" />
    <circle cx="135" cy="95" r="5" fill="#ffcc80" />
  </g>
  
  <!-- 右侧装饰：购物元素 -->
  <g transform="translate(900, 150)">
    <!-- 购物袋 -->
    <path d="M50,30 L70,30 L75,50 L45,50 L50,30 Z" fill="#d7ccc8" />
    <rect x="45" y="50" width="30" height="40" fill="#bcaaa4" />
    <path d="M50,50 C50,50 55,70 60,50 C65,70 70,50 70,50" fill="none" stroke="#8d6e63" stroke-width="1" />
    
    <!-- 衣架 -->
    <path d="M100,30 L130,80 L70,80 Z" fill="none" stroke="#8d6e63" stroke-width="2" />
    <line x1="100" y1="30" x2="100" y2="10" stroke="#8d6e63" stroke-width="2" />
    <path d="M80,80 L120,80 L120,110 L80,110 Z" fill="none" stroke="#8d6e63" stroke-width="1" />
  </g>
  
  <!-- 中央装饰：汉服展示 -->
  <g transform="translate(600, 150)">
    <!-- 汉服轮廓 -->
    <path d="M-100,0 C-80,-20 -60,-30 0,-30 C60,-30 80,-20 100,0 L80,120 L-80,120 Z" fill="#e57373" opacity="0.8" />
    <path d="M-60,-10 L-60,100 M60,-10 L60,100" fill="none" stroke="#ffcdd2" stroke-width="2" />
    <path d="M-30,-20 L-30,110 M30,-20 L30,110" fill="none" stroke="#ffcdd2" stroke-width="1" />
    
    <!-- 腰带 -->
    <rect x="-70" y="40" width="140" height="10" fill="#8d6e63" />
    <rect x="-60" y="42" width="120" height="6" fill="#d7ccc8" />
    
    <!-- 装饰花纹 -->
    <circle cx="-40" cy="20" r="5" fill="#ffcdd2" />
    <circle cx="0" cy="20" r="5" fill="#ffcdd2" />
    <circle cx="40" cy="20" r="5" fill="#ffcdd2" />
    <path d="M-40,70 C-20,60 20,60 40,70" fill="none" stroke="#ffcdd2" stroke-width="1" />
  </g>
  
  <!-- 标题区域 -->
  <g transform="translate(600, 80)">
    <!-- 标题背景 -->
    <rect x="-200" y="-40" width="400" height="80" rx="10" ry="10" fill="rgba(165, 42, 42, 0.7)" />
    
    <!-- 标题文字 -->
    <text x="0" y="0" font-family="'SimSun', serif" font-size="36" font-weight="bold" fill="#fff8e1" text-anchor="middle" dominant-baseline="middle">汉服购物</text>
    <text x="0" y="30" font-family="'KaiTi', serif" font-size="18" fill="#fff8e1" text-anchor="middle" dominant-baseline="middle">精选优质汉服品牌，享受传统文化之美</text>
  </g>
  
  <!-- 底部装饰：云纹 -->
  <g transform="translate(0, 270)">
    <path d="M0,0 C50,-20 100,20 150,0 C200,-20 250,20 300,0 C350,-20 400,20 450,0 C500,-20 550,20 600,0 C650,-20 700,20 750,0 C800,-20 850,20 900,0 C950,-20 1000,20 1050,0 C1100,-20 1150,20 1200,0" fill="none" stroke="#a67c52" stroke-width="2" />
  </g>
  
  <!-- 顶部装饰：几何纹 -->
  <g transform="translate(0, 30)">
    <path d="M0,0 L1200,0" stroke="#a67c52" stroke-width="2" />
    <path d="M50,-10 L50,10 M150,-10 L150,10 M250,-10 L250,10 M350,-10 L350,10 M450,-10 L450,10 M550,-10 L550,10 M650,-10 L650,10 M750,-10 L750,10 M850,-10 L850,10 M950,-10 L950,10 M1050,-10 L1050,10 M1150,-10 L1150,10" stroke="#a67c52" stroke-width="2" />
  </g>
  
  <!-- 点缀：价格标签 -->
  <g transform="translate(250, 200)">
    <rect x="-30" y="-20" width="60" height="40" rx="5" ry="5" fill="#ffd54f" />
    <text x="0" y="0" font-family="'Arial', sans-serif" font-size="16" font-weight="bold" fill="#5d4037" text-anchor="middle" dominant-baseline="middle">¥299</text>
    <line x1="-30" y1="-20" x2="-40" y2="-30" stroke="#ffd54f" stroke-width="2" />
  </g>
  
  <g transform="translate(800, 220)">
    <rect x="-30" y="-20" width="60" height="40" rx="5" ry="5" fill="#ffd54f" />
    <text x="0" y="0" font-family="'Arial', sans-serif" font-size="16" font-weight="bold" fill="#5d4037" text-anchor="middle" dominant-baseline="middle">¥499</text>
    <line x1="30" y1="-20" x2="40" y2="-30" stroke="#ffd54f" stroke-width="2" />
  </g>
</svg>