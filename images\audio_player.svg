<svg xmlns="http://www.w3.org/2000/svg" width="300" height="100" viewBox="0 0 300 100">
  <!-- 背景 -->
  <rect width="300" height="100" fill="#f9f5f0" rx="10" ry="10"/>
  
  <!-- 播放器边框 -->
  <rect x="5" y="5" width="290" height="90" fill="none" stroke="#8b2a2a" stroke-width="2" rx="8" ry="8"/>
  
  <!-- 播放按钮 -->
  <g transform="translate(50, 50)">
    <circle cx="0" cy="0" r="30" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="2"/>
    <polygon points="-10,-15 -10,15 15,0" fill="#8b2a2a"/>
  </g>
  
  <!-- 进度条 -->
  <g transform="translate(175, 50)">
    <rect x="-75" y="-5" width="150" height="10" fill="rgba(139, 42, 42, 0.1)" rx="5" ry="5"/>
    <rect x="-75" y="-5" width="50" height="10" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <circle cx="-25" cy="0" r="8" fill="#8b2a2a"/>
  </g>
  
  <!-- 时间显示 -->
  <g transform="translate(250, 50)">
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">02:45</text>
  </g>
  
  <!-- 标题 -->
  <g transform="translate(175, 25)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#8b2a2a">汉服历史讲解</text>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.3">
    <!-- 左上角装饰 -->
    <g transform="translate(20, 20)">
      <path d="M0 0 C5 5, 10 5, 15 0" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    </g>
    
    <!-- 右上角装饰 -->
    <g transform="translate(280, 20)">
      <path d="M0 0 C-5 5, -10 5, -15 0" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    </g>
    
    <!-- 左下角装饰 -->
    <g transform="translate(20, 80)">
      <path d="M0 0 C5 -5, 10 -5, 15 0" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    </g>
    
    <!-- 右下角装饰 -->
    <g transform="translate(280, 80)">
      <path d="M0 0 C-5 -5, -10 -5, -15 0" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    </g>
  </g>
</svg>