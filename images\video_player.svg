<svg xmlns="http://www.w3.org/2000/svg" width="600" height="400" viewBox="0 0 600 400">
  <!-- 背景 -->
  <rect width="600" height="400" fill="#f9f5f0" rx="10" ry="10"/>
  
  <!-- 视频播放器边框 -->
  <rect x="10" y="10" width="580" height="380" fill="none" stroke="#8b2a2a" stroke-width="2" rx="8" ry="8"/>
  
  <!-- 视频区域 -->
  <rect x="20" y="20" width="560" height="315" fill="rgba(139, 42, 42, 0.1)" rx="5" ry="5"/>
  
  <!-- 播放按钮 -->
  <g transform="translate(300, 177.5)">
    <circle cx="0" cy="0" r="50" fill="rgba(255, 255, 255, 0.7)" stroke="#8b2a2a" stroke-width="2"/>
    <polygon points="-20,-30 -20,30 30,0" fill="#8b2a2a"/>
  </g>
  
  <!-- 控制栏 -->
  <g transform="translate(300, 360)">
    <rect x="-280" y="-15" width="560" height="30" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    
    <!-- 播放/暂停按钮 -->
    <g transform="translate(-250, 0)">
      <rect x="-10" y="-10" width="20" height="20" fill="none" stroke="#fff" stroke-width="1" rx="2" ry="2"/>
      <rect x="-6" y="-6" width="4" height="12" fill="#fff" rx="1" ry="1"/>
      <rect x="2" y="-6" width="4" height="12" fill="#fff" rx="1" ry="1"/>
    </g>
    
    <!-- 进度条 -->
    <g transform="translate(0, 0)">
      <rect x="-200" y="-5" width="400" height="10" fill="rgba(255, 255, 255, 0.3)" rx="5" ry="5"/>
      <rect x="-200" y="-5" width="120" height="10" fill="#fff" rx="5" ry="5"/>
      <circle cx="-80" cy="0" r="8" fill="#fff"/>
    </g>
    
    <!-- 时间显示 -->
    <g transform="translate(230, 0)">
      <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#fff">03:45 / 10:30</text>
    </g>
    
    <!-- 全屏按钮 -->
    <g transform="translate(270, 0)">
      <rect x="-8" y="-8" width="16" height="16" fill="none" stroke="#fff" stroke-width="1" rx="1" ry="1"/>
      <line x1="-4" y1="-4" x2="-4" y2="4" stroke="#fff" stroke-width="1"/>
      <line x1="4" y1="-4" x2="4" y2="4" stroke="#fff" stroke-width="1"/>
      <line x1="-4" y1="-4" x2="4" y2="-4" stroke="#fff" stroke-width="1"/>
      <line x1="-4" y1="4" x2="4" y2="4" stroke="#fff" stroke-width="1"/>
    </g>
  </g>
  
  <!-- 视频标题 -->
  <g transform="translate(300, 50)">
    <rect x="-200" y="-20" width="400" height="40" fill="rgba(139, 42, 42, 0.8)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">汉服文化视频讲解</text>
  </g>
  
  <!-- 视频内容示意 -->
  <g transform="translate(300, 177.5)" opacity="0.3">
    <!-- 简化的汉服人物轮廓 -->
    <g transform="translate(-100, 0)">
      <path d="M0 -50 C-10 -50, -20 -40, -20 -30 L-20 0 L-30 30 L-20 50 L-10 60 L0 70 L10 60 L20 50 L30 30 L20 0 L20 -30 C20 -40, 10 -50, 0 -50 Z" fill="#8b2a2a"/>
      <path d="M0 -50 C-5 -60, -2 -65, 0 -70 C2 -65, 5 -60, 0 -50 Z" fill="#8b2a2a"/>
    </g>
    
    <g transform="translate(100, 0)">
      <path d="M0 -50 C-10 -50, -20 -40, -20 -30 L-20 0 L-30 30 L-20 50 L-10 60 L0 70 L10 60 L20 50 L30 30 L20 0 L20 -30 C20 -40, 10 -50, 0 -50 Z" fill="#8b2a2a"/>
      <path d="M0 -50 C-5 -60, -2 -65, 0 -70 C2 -65, 5 -60, 0 -50 Z" fill="#8b2a2a"/>
    </g>
    
    <!-- 简化的建筑轮廓 -->
    <g transform="translate(0, -30)">
      <path d="M-150 30 L-120 0 L120 0 L150 30" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M-100 30 L-100 80 L-80 80 L-80 30" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M-40 30 L-40 80 L-20 80 L-20 30" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M20 30 L20 80 L40 80 L40 30" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M80 30 L80 80 L100 80 L100 30" fill="none" stroke="#8b2a2a" stroke-width="2"/>
    </g>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.2">
    <!-- 左上角装饰 -->
    <g transform="translate(30, 30)">
      <path d="M0 0 C10 10, 20 10, 30 0 C40 10, 50 10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右上角装饰 -->
    <g transform="translate(570, 30)">
      <path d="M0 0 C-10 10, -20 10, -30 0 C-40 10, -50 10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
  </g>
</svg>