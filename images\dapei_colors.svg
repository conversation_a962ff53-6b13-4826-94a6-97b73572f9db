<svg xmlns="http://www.w3.org/2000/svg" width="800" height="500" viewBox="0 0 800 500">
  <!-- 背景 -->
  <rect width="800" height="500" fill="#f9f5f0" rx="10" ry="10"/>
  
  <!-- 装饰边框 -->
  <rect x="10" y="10" width="780" height="480" fill="none" stroke="#8b2a2a" stroke-width="2" rx="8" ry="8" stroke-dasharray="5,5"/>
  
  <!-- 标题 -->
  <g transform="translate(400, 50)">
    <rect x="-150" y="-25" width="300" height="50" fill="rgba(139, 42, 42, 0.8)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#fff">汉服色彩搭配</text>
  </g>
  
  <!-- 中央人物 -->
  <g transform="translate(400, 250)">
    <!-- 人物轮廓 -->
    <path d="M0 -120 C-20 -120, -40 -100, -40 -80 L-40 -20 L-60 40 L-40 80 L-20 100 L0 120 L20 100 L40 80 L60 40 L40 -20 L40 -80 C40 -100, 20 -120, 0 -120 Z" fill="#8b2a2a"/>
    
    <!-- 发髻 -->
    <path d="M0 -120 C-10 -130, -5 -140, 0 -150 C5 -140, 10 -130, 0 -120 Z" fill="#8b2a2a"/>
  </g>
  
  <!-- 色彩搭配示例 -->
  <!-- 左侧：传统配色 -->
  <g transform="translate(150, 200)">
    <rect x="-100" y="-20" width="200" height="40" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">传统配色</text>
    
    <!-- 色彩样本 -->
    <g transform="translate(0, 50)">
      <!-- 红色系 -->
      <g transform="translate(-60, 0)">
        <circle cx="0" cy="0" r="25" fill="#d44949"/>
        <text x="0" y="45" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">朱红</text>
      </g>
      
      <!-- 蓝色系 -->
      <g transform="translate(0, 0)">
        <circle cx="0" cy="0" r="25" fill="#4a6fa5"/>
        <text x="0" y="45" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">靛青</text>
      </g>
      
      <!-- 黄色系 -->
      <g transform="translate(60, 0)">
        <circle cx="0" cy="0" r="25" fill="#e6b422"/>
        <text x="0" y="45" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">明黄</text>
      </g>
    </g>
    
    <!-- 配色说明 -->
    <g transform="translate(0, 100)">
      <rect x="-90" y="-15" width="180" height="30" fill="rgba(139, 42, 42, 0.1)" rx="5" ry="5"/>
      <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">传统色彩富有文化象征意义</text>
    </g>
  </g>
  
  <!-- 右侧：季节配色 -->
  <g transform="translate(650, 200)">
    <rect x="-100" y="-20" width="200" height="40" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">季节配色</text>
    
    <!-- 色彩样本 -->
    <g transform="translate(0, 50)">
      <!-- 春季 -->
      <g transform="translate(-60, 0)">
        <circle cx="0" cy="0" r="25" fill="#a3d900"/>
        <text x="0" y="45" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">春季</text>
      </g>
      
      <!-- 夏季 -->
      <g transform="translate(0, 0)">
        <circle cx="0" cy="0" r="25" fill="#00a6ac"/>
        <text x="0" y="45" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">夏季</text>
      </g>
      
      <!-- 秋冬 -->
      <g transform="translate(60, 0)">
        <circle cx="0" cy="0" r="25" fill="#b97a57"/>
        <text x="0" y="45" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">秋冬</text>
      </g>
    </g>
    
    <!-- 配色说明 -->
    <g transform="translate(0, 100)">
      <rect x="-90" y="-15" width="180" height="30" fill="rgba(139, 42, 42, 0.1)" rx="5" ry="5"/>
      <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">不同季节选择不同色调</text>
    </g>
  </g>
  
  <!-- 底部：配饰搭配 -->
  <g transform="translate(400, 400)">
    <rect x="-150" y="-20" width="300" height="40" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">配饰搭配</text>
    
    <!-- 配饰图标 -->
    <g transform="translate(-200, 0)">
      <!-- 发簪 -->
      <g transform="translate(0, 30)">
        <rect x="-15" y="-2" width="30" height="4" fill="#8b2a2a"/>
        <path d="M-10 -2 L-5 -10 L5 -10 L10 -2" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="15" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">发簪</text>
      </g>
    </g>
    
    <g transform="translate(-100, 0)">
      <!-- 腰带 -->
      <g transform="translate(0, 30)">
        <rect x="-20" y="-3" width="40" height="6" fill="#8b2a2a" rx="1" ry="1"/>
        <text x="0" y="15" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">腰带</text>
      </g>
    </g>
    
    <g transform="translate(0, 0)">
      <!-- 香囊 -->
      <g transform="translate(0, 30)">
        <path d="M0 -10 L-10 0 L-5 10 L5 10 L10 0 Z" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <line x1="0" y1="-10" x2="0" y2="-15" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">香囊</text>
      </g>
    </g>
    
    <g transform="translate(100, 0)">
      <!-- 扇子 -->
      <g transform="translate(0, 30)">
        <path d="M-15 0 A15 15 0 0 1 15 0" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <line x1="0" y1="0" x2="0" y2="15" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">扇子</text>
      </g>
    </g>
    
    <g transform="translate(200, 0)">
      <!-- 首饰 -->
      <g transform="translate(0, 30)">
        <circle cx="0" cy="0" r="8" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <circle cx="0" cy="0" r="4" fill="#8b2a2a"/>
        <text x="0" y="15" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">首饰</text>
      </g>
    </g>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.2">
    <!-- 左上角装饰 -->
    <g transform="translate(50, 50)">
      <path d="M0 0 C10 10, 20 10, 30 0 C40 10, 50 10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右上角装饰 -->
    <g transform="translate(750, 50)">
      <path d="M0 0 C-10 10, -20 10, -30 0 C-40 10, -50 10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 左下角装饰 -->
    <g transform="translate(50, 450)">
      <path d="M0 0 C10 -10, 20 -10, 30 0 C40 -10, 50 -10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右下角装饰 -->
    <g transform="translate(750, 450)">
      <path d="M0 0 C-10 -10, -20 -10, -30 0 C-40 -10, -50 -10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
  </g>
</svg>