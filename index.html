<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汉服文化 - 首页</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <div class="logo">
            <img src="images/logo.svg" alt="汉服文化Logo">
            <h1>汉服文化</h1>
        </div>
        <nav>
            <ul>
                <li><a href="index.html" class="active">首页</a></li>
                <li><a href="lishi.html">汉服历史</a></li>
                <li><a href="fenlei.html">汉服分类</a></li>
                <li><a href="dapei.html">汉服搭配</a></li>
                <li><a href="huodong.html">汉服活动</a></li>
                <li><a href="gouwu.html">汉服购物</a></li>
            </ul>
        </nav>
    </header>

    <section class="banner">
        <div class="slider">
            <div class="slide current">
                <img src="images/banner1.jpg" alt="汉服文化展示">
                <div class="content">
                    <h2>传承千年文化</h2>
                    <p>探索中华传统服饰之美</p>
                </div>
            </div>
            <div class="slide">
                <img src="images/banner2.jpg" alt="汉服展示">
                <div class="content">
                    <h2>多样汉服风格</h2>
                    <p>从魏晋风华到明制风采</p>
                </div>
            </div>
            <div class="slide">
                <img src="images/banner3.jpg" alt="汉服活动">
                <div class="content">
                    <h2>汉服文化活动</h2>
                    <p>共同参与，弘扬传统</p>
                </div>
            </div>
        </div>
    </section>

    <main>
        <section class="intro">
            <h2>汉服简介</h2>
            <div class="intro-content">
                <div class="intro-text">
                    <p>汉服，全称是"汉民族传统服饰"，是从黄帝即位到公元17世纪中叶（明末清初），在汉族的主要居住区，以"华夏-汉"文化为背景和主导思想，以华夏礼仪文化为中心，通过自然演化而形成的具有独特汉民族风貌性格，明显区别于其他民族的传统服装和配饰体系，是中国"衣冠上国"、"礼仪之邦"、"锦绣中华"、赛里斯国的体现，承载了汉族的染织绣等杰出工艺和美学，传承了30多项中国非物质文化遗产以及受保护的中国工艺美术。</p>
                    <p>汉服在汉族历史上具有重要地位，是中国历史上最辉煌灿烂的文化瑰宝之一。近年来，汉服文化逐渐复兴，越来越多的年轻人开始穿着汉服，参与汉服相关活动，传承和弘扬中华优秀传统文化。</p>
                </div>
                <div class="intro-image">
                    <img src="images/intro.jpg" alt="汉服展示">
                </div>
            </div>
        </section>

        <section class="features">
            <h2>汉服特色</h2>
            <div class="feature-cards">
                <div class="card">
                    <img src="images/feature1.jpg" alt="汉服历史">
                    <h3>悠久历史</h3>
                    <p>汉服有着数千年的历史，从先秦到明朝，经历了多个朝代的演变与发展。</p>
                    <a href="lishi.html" class="btn">了解更多</a>
                </div>
                <div class="card">
                    <img src="images/feature2.jpg" alt="汉服款式">
                    <h3>多样款式</h3>
                    <p>汉服有多种款式，如深衣、襦裙、袄裙、曲裾、直裾、圆领袍等，适合不同场合穿着。</p>
                    <a href="fenlei.html" class="btn">了解更多</a>
                </div>
                <div class="card">
                    <img src="images/feature3.jpg" alt="汉服文化">
                    <h3>文化内涵</h3>
                    <p>汉服不仅是服饰，更是中华文化的载体，体现了中国传统的礼仪、美学和哲学思想。</p>
                    <a href="huodong.html" class="btn">了解更多</a>
                </div>
            </div>
        </section>

        <section class="video-section">
            <h2>汉服文化视频</h2>
            <div class="video-container">
                <video controls>
                    <source src="media/hanfu_video.mp4" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            </div>
        </section>

        <section class="news">
            <h2>最新动态</h2>
            <div class="news-container">
                <div class="news-item">
                    <div class="news-date">2023-10-15</div>
                    <h3>2023年全国汉服文化节即将举行</h3>
                    <p>第五届全国汉服文化节将于下月在西安举行，届时将有来自全国各地的汉服爱好者参与...</p>
                    <a href="huodong.html" class="read-more">阅读更多</a>
                </div>
                <div class="news-item">
                    <div class="news-date">2023-09-28</div>
                    <h3>汉服设计大赛圆满结束</h3>
                    <p>为期一个月的全国汉服设计大赛已圆满结束，共收到来自全国各地的500余件参赛作品...</p>
                    <a href="huodong.html" class="read-more">阅读更多</a>
                </div>
                <div class="news-item">
                    <div class="news-date">2023-09-10</div>
                    <h3>秋季汉服新品发布</h3>
                    <p>多家知名汉服品牌发布秋季新品，融合传统与现代元素，展现汉服的多样魅力...</p>
                    <a href="gouwu.html" class="read-more">阅读更多</a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section about">
                <h3>关于我们</h3>
                <p>我们致力于传承和弘扬汉服文化，推动汉服在现代社会的复兴与发展。</p>
                <div class="contact">
                    <span><i class="fa fa-phone"></i> 123-456-789</span>
                    <span><i class="fa fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></span>
                </div>
            </div>
            <div class="footer-section links">
                <h3>友情链接</h3>
                <ul>
                    <li><a href="https://www.bilibili.com/" target="_blank">哔哩哔哩</a></li>
                    <li><a href="https://www.zhihu.com/" target="_blank">知乎</a></li>
                    <li><a href="https://www.douban.com/" target="_blank">豆瓣</a></li>
                </ul>
            </div>
            <div class="footer-section contact-form">
                <h3>联系我们</h3>
                <form action="#" method="post">
                    <input type="email" name="email" class="text-input contact-input" placeholder="您的邮箱地址...">
                    <textarea name="message" class="text-input contact-input" placeholder="您的留言..."></textarea>
                    <button type="submit" class="btn btn-big">发送</button>
                </form>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 汉服文化网站 | 设计与开发</p>
        </div>
    </footer>

    <script>
        // 简单的轮播图效果，使用CSS实现主要效果，JS只用于切换类名
        const slides = document.querySelectorAll('.slide');
        let currentSlide = 0;
        
        function nextSlide() {
            slides[currentSlide].classList.remove('current');
            currentSlide = (currentSlide + 1) % slides.length;
            slides[currentSlide].classList.add('current');
        }
        
        // 自动轮播
        setInterval(nextSlide, 5000);
    </script>
</body>
</html>