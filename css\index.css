/* 首页样式 - 包含公共样式和首页专用样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f5f0;
}

a {
    text-decoration: none;
    color: #8b2a2a;
    transition: color 0.3s ease;
}

a:hover {
    color: #d44949;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #8b2a2a;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #d44949;
}

.btn-big {
    padding: 10px 20px;
    font-size: 1.1em;
}

/* 头部样式 */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5%;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.8em;
    color: #8b2a2a;
    font-weight: bold;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    font-size: 1.1em;
    padding: 5px 10px;
    border-radius: 4px;
}

nav ul li a.active {
    background-color: #8b2a2a;
    color: #fff;
}

/* 通用章节标题样式 */
section h2 {
    text-align: center;
    font-size: 2em;
    margin-bottom: 30px;
    color: #8b2a2a;
    position: relative;
}

section h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #8b2a2a;
    margin: 10px auto;
}

/* 通用卡片样式 */
.card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* 视频和音频容器通用样式 */
.video-container, .audio-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.video-container video, .audio-container audio {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.audio-description {
    color: #666;
    font-style: italic;
    margin-top: 15px;
}

/* 页脚样式 */
footer {
    background-color: #333;
    color: #fff;
    padding: 40px 0 0;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-section {
    flex: 1;
    min-width: 300px;
    margin-bottom: 30px;
}

.footer-section h3 {
    font-size: 1.3em;
    margin-bottom: 15px;
    position: relative;
}

.footer-section h3:after {
    content: '';
    display: block;
    width: 30px;
    height: 2px;
    background-color: #8b2a2a;
    margin-top: 5px;
}

.footer-section p {
    margin-bottom: 15px;
}

.contact span {
    display: block;
    margin-bottom: 10px;
}

.footer-section.links ul li {
    margin-bottom: 10px;
}

.text-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 4px;
}

textarea.text-input {
    height: 100px;
    resize: none;
}

.footer-bottom {
    background-color: #222;
    padding: 10px 0;
    text-align: center;
    font-size: 0.9em;
}

/* 首页专用样式 */

/* 轮播图样式 - 纯CSS实现 */
.banner {
    position: relative;
    height: 500px;
    overflow: hidden;
}

.slider {
    height: 100%;
    position: relative;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    animation: slideShow 15s infinite;
}

/* 轮播动画 - 每张图片显示5秒，总共3张图片 */
.slide:nth-child(1) {
    animation-delay: 0s;
    opacity: 1; /* 默认显示第一张 */
}

.slide:nth-child(2) {
    animation-delay: 5s;
}

.slide:nth-child(3) {
    animation-delay: 10s;
}

@keyframes slideShow {
    0%, 33.33% {
        opacity: 1;
    }
    33.34%, 100% {
        opacity: 0;
    }
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide .content {
    position: absolute;
    bottom: 70px;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 20px;
    text-align: center;
}

.slide .content h2 {
    margin-bottom: 10px;
    font-size: 2em;
}

/* 首页内容区域 */
.intro, .culture-value, .features, .craftsmanship, .video-section, .revival, .news {
    margin: 60px 0;
    position: relative;
}

/* 章节副标题 */
.section-subtitle {
    text-align: center;
    font-size: 1.2em;
    color: #666;
    margin-bottom: 40px;
    font-style: italic;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* 简介部分 */
.intro-content {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 40px;
}

.intro-text {
    flex: 2;
    min-width: 400px;
}

.intro-highlight {
    background: linear-gradient(135deg, #8b2a2a, #d44949);
    color: white;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.intro-highlight h3 {
    margin-bottom: 15px;
    font-size: 1.5em;
}

.highlight-text {
    font-size: 1.1em;
    line-height: 1.6;
    margin: 0;
}

.intro-details p {
    margin-bottom: 20px;
    text-align: justify;
    line-height: 1.8;
    color: #555;
    font-size: 1.05em;
}

.intro-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f8f8;
    border-radius: 10px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5em;
    font-weight: bold;
    color: #8b2a2a;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9em;
    color: #666;
}

.intro-image {
    flex: 1;
    min-width: 300px;
    text-align: center;
    position: relative;
}

.intro-image img {
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
}

.intro-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9em;
}

/* 文化价值部分 */
.value-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.value-item {
    background-color: #fff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.value-icon {
    font-size: 3em;
    margin-bottom: 20px;
    display: block;
}

.value-item h3 {
    color: #8b2a2a;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.value-item p {
    color: #666;
    line-height: 1.7;
    font-size: 1.05em;
}

/* 特色卡片 */
.feature-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.feature-cards .card {
    width: 350px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.feature-cards .card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.card-image {
    position: relative;
    height: 220px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-image img {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    top: 15px;
    right: 15px;
}

.card-tag {
    background-color: rgba(139, 42, 42, 0.9);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: bold;
}

.card-content {
    padding: 25px;
}

.card-content h3 {
    font-size: 1.4em;
    color: #8b2a2a;
    margin-bottom: 15px;
}

.card-content p {
    color: #666;
    line-height: 1.7;
    margin-bottom: 20px;
    font-size: 1.05em;
}

.card-features {
    margin-bottom: 20px;
}

.feature-tag {
    display: inline-block;
    background-color: #f0f0f0;
    color: #666;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85em;
    margin-right: 8px;
    margin-bottom: 5px;
}

/* 最新动态优化 */
.news-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.news-item {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-wrap: wrap;
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.news-item.featured {
    border: 2px solid #8b2a2a;
}

.news-image {
    width: 300px;
    height: 200px;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-item:hover .news-image img {
    transform: scale(1.1);
}

.news-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: rgba(139, 42, 42, 0.9);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: bold;
}

.news-content {
    flex: 1;
    padding: 25px;
    min-width: 300px;
}

.news-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 0.9em;
    color: #999;
}

.news-date {
    color: #8b2a2a;
    font-weight: bold;
}

.news-author {
    color: #666;
}

.news-content h3 {
    margin-bottom: 15px;
    font-size: 1.4em;
    color: #333;
    line-height: 1.4;
}

.news-content p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.7;
    font-size: 1.05em;
    text-align: justify;
}

.news-tags {
    margin-bottom: 20px;
}

.tag {
    display: inline-block;
    background-color: #f0f0f0;
    color: #666;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85em;
    margin-right: 8px;
    margin-bottom: 5px;
}

.read-more {
    font-weight: bold;
    color: #8b2a2a;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: #d44949;
}

.news-more {
    text-align: center;
    margin-top: 40px;
}

/* 响应式设计 - 公共响应式样式 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        padding: 10px;
    }

    .logo {
        margin-bottom: 10px;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    nav ul li {
        margin: 5px;
    }

    .footer-content {
        flex-direction: column;
    }

    .footer-section {
        min-width: auto;
    }

    /* 首页响应式样式 */
    .banner {
        height: 300px;
    }

    .slide .content {
        bottom: 30px;
        padding: 15px;
    }

    .slide .content h2 {
        font-size: 1.5em;
    }

    .intro-content {
        flex-direction: column;
    }

    .feature-cards {
        flex-direction: column;
        align-items: center;
    }

    .feature-cards .card {
        width: 100%;
        max-width: 350px;
    }

    .news-container {
        flex-direction: column;
        align-items: center;
    }

    .news-item {
        width: 100%;
        max-width: 400px;
    }
}

@media (max-width: 480px) {
    .banner {
        height: 250px;
    }

    .slide .content h2 {
        font-size: 1.2em;
    }

    .slide .content p {
        font-size: 0.9em;
    }

    .intro-text {
        min-width: auto;
    }

    .intro-stats {
        flex-direction: column;
        gap: 15px;
    }

    .stat-number {
        font-size: 2em;
    }

    .value-grid {
        grid-template-columns: 1fr;
    }

    .feature-cards .card {
        width: 100%;
        max-width: 350px;
    }

    .craft-item {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .video-content {
        flex-direction: column;
    }

    .video-container {
        min-width: auto;
    }

    .video-features {
        min-width: auto;
        flex-direction: row;
        flex-wrap: wrap;
    }

    .revival-content {
        flex-direction: column;
    }

    .revival-text {
        min-width: auto;
    }

    .revival-highlights {
        flex-direction: column;
        gap: 15px;
    }

    .revival-image {
        min-width: auto;
    }

    .news-item {
        flex-direction: column;
    }

    .news-image {
        width: 100%;
        height: 200px;
    }

    .news-content {
        min-width: auto;
    }
}

/* 传统工艺部分 */
.craft-timeline {
    display: flex;
    flex-direction: column;
    gap: 30px;
    max-width: 800px;
    margin: 0 auto;
}

.craft-item {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 25px;
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.craft-item:hover {
    transform: translateX(10px);
}

.craft-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #8b2a2a, #d44949);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    font-weight: bold;
    flex-shrink: 0;
}

.craft-content h3 {
    color: #8b2a2a;
    margin-bottom: 10px;
    font-size: 1.3em;
}

.craft-content p {
    color: #666;
    line-height: 1.7;
    font-size: 1.05em;
}

/* 视频部分优化 */
.video-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    align-items: flex-start;
}

.video-container {
    flex: 2;
    min-width: 400px;
}

.video-container video {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.video-info {
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f8f8;
    border-radius: 10px;
}

.video-info h3 {
    color: #8b2a2a;
    margin-bottom: 10px;
    font-size: 1.3em;
}

.video-info p {
    color: #666;
    line-height: 1.7;
}

.video-features {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.video-feature {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.feature-icon {
    font-size: 2em;
    margin-bottom: 10px;
    display: block;
}

.video-feature h4 {
    color: #8b2a2a;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.video-feature p {
    color: #666;
    font-size: 0.95em;
    line-height: 1.6;
}

/* 汉服复兴部分 */
.revival-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    align-items: flex-start;
}

.revival-text {
    flex: 2;
    min-width: 400px;
}

.revival-text h3 {
    color: #8b2a2a;
    margin-bottom: 20px;
    font-size: 1.6em;
}

.revival-text p {
    color: #555;
    line-height: 1.8;
    margin-bottom: 30px;
    font-size: 1.05em;
    text-align: justify;
}

.revival-highlights {
    display: flex;
    justify-content: space-around;
    margin: 30px 0;
    padding: 25px;
    background: linear-gradient(135deg, #8b2a2a, #d44949);
    border-radius: 15px;
    color: white;
}

.highlight-item {
    text-align: center;
}

.highlight-number {
    display: block;
    font-size: 2.2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.highlight-text {
    font-size: 0.95em;
}

.revival-trends h4 {
    color: #8b2a2a;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.revival-trends ul {
    list-style: none;
    padding: 0;
}

.revival-trends li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    color: #666;
    line-height: 1.6;
}

.revival-trends li:last-child {
    border-bottom: none;
}

.revival-image {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.revival-image img {
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
}

.revival-image:hover img {
    transform: scale(1.05);
}

.image-caption {
    margin-top: 15px;
    color: #666;
    font-style: italic;
    font-size: 0.95em;
}
