/* 首页专用样式 */

/* 轮播图样式 - 纯CSS实现 */
.banner {
    position: relative;
    height: 500px;
    overflow: hidden;
}

.slider {
    height: 100%;
    position: relative;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    animation: slideShow 15s infinite;
}

/* 轮播动画 - 每张图片显示5秒，总共3张图片 */
.slide:nth-child(1) {
    animation-delay: 0s;
    opacity: 1; /* 默认显示第一张 */
}

.slide:nth-child(2) {
    animation-delay: 5s;
}

.slide:nth-child(3) {
    animation-delay: 10s;
}

@keyframes slideShow {
    0%, 33.33% {
        opacity: 1;
    }
    33.34%, 100% {
        opacity: 0;
    }
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide .content {
    position: absolute;
    bottom: 70px;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 20px;
    text-align: center;
}

.slide .content h2 {
    margin-bottom: 10px;
    font-size: 2em;
}

/* 首页内容区域 */
.intro, .features, .video-section, .news {
    margin: 40px 0;
}

/* 简介部分 */
.intro-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 30px;
}

.intro-text {
    flex: 1;
    min-width: 300px;
}

.intro-text p {
    margin-bottom: 15px;
    text-align: justify;
    line-height: 1.8;
}

.intro-image {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.intro-image img {
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 特色卡片 */
.feature-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.feature-cards .card {
    width: 300px;
    transition: transform 0.3s ease;
}

.feature-cards .card:hover {
    transform: translateY(-10px);
}

.feature-cards .card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.feature-cards .card h3 {
    padding: 15px 15px 5px;
    font-size: 1.3em;
    color: #8b2a2a;
}

.feature-cards .card p {
    padding: 0 15px 15px;
    color: #666;
    line-height: 1.6;
}

.feature-cards .card .btn {
    margin: 0 15px 15px;
}

/* 最新动态 */
.news-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.news-item {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    width: 350px;
    transition: transform 0.3s ease;
}

.news-item:hover {
    transform: translateY(-5px);
}

.news-date {
    color: #8b2a2a;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 0.9em;
}

.news-item h3 {
    margin-bottom: 10px;
    font-size: 1.3em;
    color: #333;
}

.news-item p {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.6;
}

.read-more {
    font-weight: bold;
    color: #8b2a2a;
}

.read-more:hover {
    color: #d44949;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .banner {
        height: 300px;
    }

    .slide .content {
        bottom: 30px;
        padding: 15px;
    }

    .slide .content h2 {
        font-size: 1.5em;
    }

    .intro-content {
        flex-direction: column;
    }

    .feature-cards {
        flex-direction: column;
        align-items: center;
    }

    .feature-cards .card {
        width: 100%;
        max-width: 350px;
    }

    .news-container {
        flex-direction: column;
        align-items: center;
    }

    .news-item {
        width: 100%;
        max-width: 400px;
    }
}

@media (max-width: 480px) {
    .banner {
        height: 250px;
    }

    .slide .content h2 {
        font-size: 1.2em;
    }

    .slide .content p {
        font-size: 0.9em;
    }
}
