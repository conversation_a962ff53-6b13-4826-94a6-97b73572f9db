/* 首页样式 - 包含公共样式和首页专用样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f5f0;
}

a {
    text-decoration: none;
    color: #8b2a2a;
    transition: color 0.3s ease;
}

a:hover {
    color: #d44949;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #8b2a2a;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #d44949;
}

.btn-big {
    padding: 10px 20px;
    font-size: 1.1em;
}

/* 头部样式 */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5%;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.8em;
    color: #8b2a2a;
    font-weight: bold;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    font-size: 1.1em;
    padding: 5px 10px;
    border-radius: 4px;
}

nav ul li a.active {
    background-color: #8b2a2a;
    color: #fff;
}

/* 通用章节标题样式 */
section h2 {
    text-align: center;
    font-size: 2em;
    margin-bottom: 30px;
    color: #8b2a2a;
    position: relative;
}

section h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #8b2a2a;
    margin: 10px auto;
}

/* 通用卡片样式 */
.card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* 视频和音频容器通用样式 */
.video-container, .audio-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.video-container video, .audio-container audio {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.audio-description {
    color: #666;
    font-style: italic;
    margin-top: 15px;
}

/* 页脚样式 */
footer {
    background-color: #333;
    color: #fff;
    padding: 40px 0 0;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-section {
    flex: 1;
    min-width: 300px;
    margin-bottom: 30px;
}

.footer-section h3 {
    font-size: 1.3em;
    margin-bottom: 15px;
    position: relative;
}

.footer-section h3:after {
    content: '';
    display: block;
    width: 30px;
    height: 2px;
    background-color: #8b2a2a;
    margin-top: 5px;
}

.footer-section p {
    margin-bottom: 15px;
}

.contact span {
    display: block;
    margin-bottom: 10px;
}

.footer-section.links ul li {
    margin-bottom: 10px;
}

.text-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 4px;
}

textarea.text-input {
    height: 100px;
    resize: none;
}

.footer-bottom {
    background-color: #222;
    padding: 10px 0;
    text-align: center;
    font-size: 0.9em;
}

/* 首页专用样式 */

/* 轮播图样式 - 纯CSS实现 */
.banner {
    position: relative;
    height: 500px;
    overflow: hidden;
}

.slider {
    height: 100%;
    position: relative;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    animation: slideShow 15s infinite;
}

/* 轮播动画 - 每张图片显示5秒，总共3张图片 */
.slide:nth-child(1) {
    animation-delay: 0s;
    opacity: 1; /* 默认显示第一张 */
}

.slide:nth-child(2) {
    animation-delay: 5s;
}

.slide:nth-child(3) {
    animation-delay: 10s;
}

@keyframes slideShow {
    0%, 33.33% {
        opacity: 1;
    }
    33.34%, 100% {
        opacity: 0;
    }
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide .content {
    position: absolute;
    bottom: 70px;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 20px;
    text-align: center;
}

.slide .content h2 {
    margin-bottom: 10px;
    font-size: 2em;
}

/* 首页内容区域 */
.intro, .features, .video-section, .news {
    margin: 40px 0;
}

/* 简介部分 */
.intro-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 30px;
}

.intro-text {
    flex: 1;
    min-width: 300px;
}

.intro-text p {
    margin-bottom: 15px;
    text-align: justify;
    line-height: 1.8;
}

.intro-image {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.intro-image img {
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 特色卡片 */
.feature-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.feature-cards .card {
    width: 300px;
    transition: transform 0.3s ease;
}

.feature-cards .card:hover {
    transform: translateY(-10px);
}

.feature-cards .card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.feature-cards .card h3 {
    padding: 15px 15px 5px;
    font-size: 1.3em;
    color: #8b2a2a;
}

.feature-cards .card p {
    padding: 0 15px 15px;
    color: #666;
    line-height: 1.6;
}

.feature-cards .card .btn {
    margin: 0 15px 15px;
}

/* 最新动态 */
.news-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.news-item {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    width: 350px;
    transition: transform 0.3s ease;
}

.news-item:hover {
    transform: translateY(-5px);
}

.news-date {
    color: #8b2a2a;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 0.9em;
}

.news-item h3 {
    margin-bottom: 10px;
    font-size: 1.3em;
    color: #333;
}

.news-item p {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.6;
}

.read-more {
    font-weight: bold;
    color: #8b2a2a;
}

.read-more:hover {
    color: #d44949;
}

/* 响应式设计 - 公共响应式样式 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        padding: 10px;
    }

    .logo {
        margin-bottom: 10px;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    nav ul li {
        margin: 5px;
    }

    .footer-content {
        flex-direction: column;
    }

    .footer-section {
        min-width: auto;
    }

    /* 首页响应式样式 */
    .banner {
        height: 300px;
    }

    .slide .content {
        bottom: 30px;
        padding: 15px;
    }

    .slide .content h2 {
        font-size: 1.5em;
    }

    .intro-content {
        flex-direction: column;
    }

    .feature-cards {
        flex-direction: column;
        align-items: center;
    }

    .feature-cards .card {
        width: 100%;
        max-width: 350px;
    }

    .news-container {
        flex-direction: column;
        align-items: center;
    }

    .news-item {
        width: 100%;
        max-width: 400px;
    }
}

@media (max-width: 480px) {
    .banner {
        height: 250px;
    }

    .slide .content h2 {
        font-size: 1.2em;
    }

    .slide .content p {
        font-size: 0.9em;
    }
}
