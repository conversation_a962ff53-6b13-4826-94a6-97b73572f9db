/* 分类页面专用样式 */

/* 分类介绍部分 */
.category-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.category-intro p {
    line-height: 1.8;
    color: #555;
    font-size: 1.1em;
}

/* 标签页样式 - 纯CSS实现 */
.tabs {
    max-width: 1000px;
    margin: 0 auto;
}

/* 隐藏单选按钮 */
.tab-radio {
    display: none;
}

.tab-header {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 12px 20px;
    background-color: #f0f0f0;
    cursor: pointer;
    border-radius: 25px;
    margin: 5px;
    transition: all 0.3s ease;
    font-weight: bold;
    border: 2px solid transparent;
    display: inline-block;
}

.tab-btn:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

/* 当对应的单选按钮被选中时，标签按钮变为激活状态 */
.tab-radio:checked + .tab-btn {
    background-color: #8b2a2a;
    color: #fff;
    border-color: #8b2a2a;
}

/* 标签页内容 */
.tab-pane {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

/* 当对应的单选按钮被选中时，显示对应的标签页内容 */
#dynasty-radio:checked ~ .tab-content #dynasty,
#gender-radio:checked ~ .tab-content #gender,
#occasion-radio:checked ~ .tab-content #occasion,
#structure-radio:checked ~ .tab-content #structure {
    display: block;
}

/* 默认显示第一个标签页（朝代分类） */
#dynasty {
    display: block;
}

.tab-pane h3 {
    text-align: center;
    margin-bottom: 30px;
    color: #8b2a2a;
    font-size: 1.8em;
}

/* 分类项目样式 */
.category-items {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.category-item {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.category-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.category-item img {
    width: 300px;
    height: 300px;
    object-fit: cover;
    flex-shrink: 0;
}

.category-info {
    flex: 1;
    padding: 25px;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.category-info h4 {
    font-size: 1.6em;
    margin-bottom: 15px;
    color: #8b2a2a;
    border-bottom: 2px solid #8b2a2a;
    padding-bottom: 5px;
}

.category-info p {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #555;
    text-align: justify;
}

.category-info ul {
    margin-left: 0;
    list-style: none;
}

.category-info ul li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
    color: #666;
}

.category-info ul li:before {
    content: "•";
    color: #8b2a2a;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.category-info ul li strong {
    color: #8b2a2a;
}

/* 配饰部分样式 */
.category-detail {
    margin: 50px 0;
}

.accessory-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}

.accessory-item {
    width: 250px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
    text-align: center;
}

.accessory-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accessory-item img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.accessory-item h3 {
    padding: 15px 15px 10px;
    color: #8b2a2a;
    font-size: 1.3em;
}

.accessory-item p {
    padding: 0 15px 20px;
    color: #666;
    line-height: 1.6;
    font-size: 0.95em;
}

/* 视频部分样式 */
.video-section {
    margin: 50px 0;
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.video-description {
    text-align: center;
    margin-top: 15px;
    color: #666;
    font-style: italic;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tab-header {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 200px;
        text-align: center;
    }

    .category-item {
        flex-direction: column;
    }

    .category-item img {
        width: 100%;
        height: 250px;
    }

    .category-info {
        min-width: auto;
        padding: 20px;
    }

    .accessory-items {
        flex-direction: column;
        align-items: center;
    }

    .accessory-item {
        width: 100%;
        max-width: 350px;
    }
}

@media (max-width: 480px) {
    .category-intro {
        margin: 0 10px 30px;
        padding: 15px;
    }

    .category-info h4 {
        font-size: 1.3em;
    }

    .category-info {
        padding: 15px;
    }

    .accessory-item h3 {
        font-size: 1.1em;
    }
}
