/* 分类页面样式 - 包含公共样式和分类页面专用样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f5f0;
}

a {
    text-decoration: none;
    color: #8b2a2a;
    transition: color 0.3s ease;
}

a:hover {
    color: #d44949;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #8b2a2a;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #d44949;
}

.btn-big {
    padding: 10px 20px;
    font-size: 1.1em;
}

/* 头部样式 */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5%;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.8em;
    color: #8b2a2a;
    font-weight: bold;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    font-size: 1.1em;
    padding: 5px 10px;
    border-radius: 4px;
}

nav ul li a.active {
    background-color: #8b2a2a;
    color: #fff;
}

/* 页面横幅通用样式 */
.page-banner {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.page-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 20px;
    border-radius: 5px;
    width: 80%;
    max-width: 800px;
}

.page-title h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

/* 通用章节标题样式 */
section h2 {
    text-align: center;
    font-size: 2em;
    margin-bottom: 30px;
    color: #8b2a2a;
    position: relative;
}

section h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #8b2a2a;
    margin: 10px auto;
}

/* 通用卡片样式 */
.card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* 视频和音频容器通用样式 */
.video-container, .audio-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.video-container video, .audio-container audio {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.audio-description {
    color: #666;
    font-style: italic;
    margin-top: 15px;
}

/* 页脚样式 */
footer {
    background-color: #333;
    color: #fff;
    padding: 40px 0 0;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-section {
    flex: 1;
    min-width: 300px;
    margin-bottom: 30px;
}

.footer-section h3 {
    font-size: 1.3em;
    margin-bottom: 15px;
    position: relative;
}

.footer-section h3:after {
    content: '';
    display: block;
    width: 30px;
    height: 2px;
    background-color: #8b2a2a;
    margin-top: 5px;
}

.footer-section p {
    margin-bottom: 15px;
}

.contact span {
    display: block;
    margin-bottom: 10px;
}

.footer-section.links ul li {
    margin-bottom: 10px;
}

.text-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 4px;
}

textarea.text-input {
    height: 100px;
    resize: none;
}

.footer-bottom {
    background-color: #222;
    padding: 10px 0;
    text-align: center;
    font-size: 0.9em;
}

/* 分类页面专用样式 */

/* 分类介绍部分 */
.category-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.category-intro p {
    line-height: 1.8;
    color: #555;
    font-size: 1.1em;
}

/* 标签页样式 - 纯CSS实现 */
.tabs {
    max-width: 1000px;
    margin: 0 auto;
}

/* 隐藏单选按钮 */
.tab-radio {
    display: none;
}

.tab-header {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 12px 20px;
    background-color: #f0f0f0;
    cursor: pointer;
    border-radius: 25px;
    margin: 5px;
    transition: all 0.3s ease;
    font-weight: bold;
    border: 2px solid transparent;
    display: inline-block;
}

.tab-btn:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

/* 当对应的单选按钮被选中时，标签按钮变为激活状态 */
.tab-radio:checked + .tab-btn {
    background-color: #8b2a2a;
    color: #fff;
    border-color: #8b2a2a;
}

/* 标签页内容 */
.tab-pane {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

/* 当对应的单选按钮被选中时，显示对应的标签页内容 */
#dynasty-radio:checked ~ .tab-content #dynasty,
#gender-radio:checked ~ .tab-content #gender,
#occasion-radio:checked ~ .tab-content #occasion,
#structure-radio:checked ~ .tab-content #structure {
    display: block;
}

/* 默认显示第一个标签页（朝代分类） */
#dynasty {
    display: block;
}

.tab-pane h3 {
    text-align: center;
    margin-bottom: 30px;
    color: #8b2a2a;
    font-size: 1.8em;
}

/* 分类项目样式 */
.category-items {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.category-item {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.category-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.category-item img {
    width: 300px;
    height: 300px;
    object-fit: cover;
    flex-shrink: 0;
}

.category-info {
    flex: 1;
    padding: 25px;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.category-info h4 {
    font-size: 1.6em;
    margin-bottom: 15px;
    color: #8b2a2a;
    border-bottom: 2px solid #8b2a2a;
    padding-bottom: 5px;
}

.category-info p {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #555;
    text-align: justify;
}

.category-info ul {
    margin-left: 0;
    list-style: none;
}

.category-info ul li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
    color: #666;
}

.category-info ul li:before {
    content: "•";
    color: #8b2a2a;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.category-info ul li strong {
    color: #8b2a2a;
}

/* 配饰部分样式 */
.category-detail {
    margin: 50px 0;
}

.accessory-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}

.accessory-item {
    width: 250px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
    text-align: center;
}

.accessory-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accessory-item img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.accessory-item h3 {
    padding: 15px 15px 10px;
    color: #8b2a2a;
    font-size: 1.3em;
}

.accessory-item p {
    padding: 0 15px 20px;
    color: #666;
    line-height: 1.6;
    font-size: 0.95em;
}

/* 视频部分样式 */
.video-section {
    margin: 50px 0;
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.video-description {
    text-align: center;
    margin-top: 15px;
    color: #666;
    font-style: italic;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 - 公共响应式样式 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        padding: 10px;
    }

    .logo {
        margin-bottom: 10px;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    nav ul li {
        margin: 5px;
    }

    .page-banner {
        height: 200px;
    }

    .page-title h1 {
        font-size: 1.8em;
    }

    .footer-content {
        flex-direction: column;
    }

    .footer-section {
        min-width: auto;
    }

    /* 分类页面响应式样式 */
    .tab-header {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 200px;
        text-align: center;
    }

    .category-item {
        flex-direction: column;
    }

    .category-item img {
        width: 100%;
        height: 250px;
    }

    .category-info {
        min-width: auto;
        padding: 20px;
    }

    .accessory-items {
        flex-direction: column;
        align-items: center;
    }

    .accessory-item {
        width: 100%;
        max-width: 350px;
    }
}

@media (max-width: 480px) {
    .category-intro {
        margin: 0 10px 30px;
        padding: 15px;
    }

    .category-info h4 {
        font-size: 1.3em;
    }

    .category-info {
        padding: 15px;
    }

    .accessory-item h3 {
        font-size: 1.1em;
    }
}
