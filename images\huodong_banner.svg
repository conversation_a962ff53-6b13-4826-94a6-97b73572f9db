<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="300" viewBox="0 0 1200 300">
  <!-- 背景 -->
  <rect width="1200" height="300" fill="#f9eee2"/>
  
  <!-- 装饰边框 -->
  <rect x="10" y="10" width="1180" height="280" fill="none" stroke="#8b2a2a" stroke-width="2" rx="5" ry="5" stroke-dasharray="10,5"/>
  
  <!-- 中央图案：汉服活动场景 -->
  <g transform="translate(600, 150)">
    <!-- 中央建筑 - 传统亭子 -->
    <g transform="translate(0, -20)">
      <path d="M0 -60 L-100 -20 L-100 20 L100 20 L100 -20 Z" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M-80 20 L-80 80 L-60 80 L-60 20 Z" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M-20 20 L-20 80 L0 80 L0 20 Z" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M40 20 L40 80 L60 80 L60 20 Z" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <path d="M-100 -20 L-120 -30 L0 -70 L120 -30 L100 -20 Z" fill="none" stroke="#8b2a2a" stroke-width="2"/>
    </g>
    
    <!-- 左侧活动类型 -->
    <g transform="translate(-300, 0)">
      <!-- 祭祀活动 -->
      <g transform="translate(0, -60)">
        <rect x="-50" y="-25" width="100" height="50" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" text-anchor="middle" fill="#8b2a2a">祭祀活动</text>
      </g>
      
      <!-- 文化交流 -->
      <g transform="translate(0, 0)">
        <rect x="-50" y="-25" width="100" height="50" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" text-anchor="middle" fill="#8b2a2a">文化交流</text>
      </g>
      
      <!-- 摄影雅集 -->
      <g transform="translate(0, 60)">
        <rect x="-50" y="-25" width="100" height="50" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" text-anchor="middle" fill="#8b2a2a">摄影雅集</text>
      </g>
    </g>
    
    <!-- 右侧活动场所 -->
    <g transform="translate(300, 0)">
      <!-- 公园 -->
      <g transform="translate(0, -60)">
        <rect x="-50" y="-25" width="100" height="50" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" text-anchor="middle" fill="#8b2a2a">公园</text>
      </g>
      
      <!-- 古建筑 -->
      <g transform="translate(0, 0)">
        <rect x="-50" y="-25" width="100" height="50" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" text-anchor="middle" fill="#8b2a2a">古建筑</text>
      </g>
      
      <!-- 文化中心 -->
      <g transform="translate(0, 60)">
        <rect x="-50" y="-25" width="100" height="50" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" text-anchor="middle" fill="#8b2a2a">文化中心</text>
      </g>
    </g>
    
    <!-- 人物剪影组 -->
    <g transform="translate(-50, 30)">
      <!-- 人物1 -->
      <path d="M0 -40 C-5 -40, -10 -35, -10 -30 L-10 0 L-15 10 L-10 20 L-5 25 L0 30 L5 25 L10 20 L15 10 L10 0 L10 -30 C10 -35, 5 -40, 0 -40 Z" fill="#8b2a2a"/>
    </g>
    
    <g transform="translate(0, 30)">
      <!-- 人物2 -->
      <path d="M0 -40 C-5 -40, -10 -35, -10 -30 L-10 0 L-15 10 L-10 20 L-5 25 L0 30 L5 25 L10 20 L15 10 L10 0 L10 -30 C10 -35, 5 -40, 0 -40 Z" fill="#8b2a2a"/>
    </g>
    
    <g transform="translate(50, 30)">
      <!-- 人物3 -->
      <path d="M0 -40 C-5 -40, -10 -35, -10 -30 L-10 0 L-15 10 L-10 20 L-5 25 L0 30 L5 25 L10 20 L15 10 L10 0 L10 -30 C10 -35, 5 -40, 0 -40 Z" fill="#8b2a2a"/>
    </g>
    
    <!-- 连接线 -->
    <line x1="-150" y1="-60" x2="-100" y2="-20" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    <line x1="-150" y1="0" x2="-100" y2="0" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    <line x1="-150" y1="60" x2="-100" y2="20" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    
    <line x1="150" y1="-60" x2="100" y2="-20" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    <line x1="150" y1="0" x2="100" y2="0" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    <line x1="150" y1="60" x2="100" y2="20" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
  </g>
  
  <!-- 标题 -->
  <g transform="translate(600, 50)">
    <rect x="-200" y="-30" width="400" height="60" fill="rgba(139, 42, 42, 0.8)" rx="10" ry="10"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="#fff">汉服文化活动</text>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.2">
    <!-- 左上角装饰：灯笼 -->
    <g transform="translate(50, 50)">
      <path d="M0 0 C-10 0, -15 10, -15 20 C-15 30, -10 40, 0 40 C10 40, 15 30, 15 20 C15 10, 10 0, 0 0 Z" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <line x1="0" y1="-10" x2="0" y2="0" stroke="#8b2a2a" stroke-width="2"/>
      <line x1="0" y1="40" x2="0" y2="50" stroke="#8b2a2a" stroke-width="2"/>
    </g>
    
    <!-- 右上角装饰：灯笼 -->
    <g transform="translate(1150, 50)">
      <path d="M0 0 C-10 0, -15 10, -15 20 C-15 30, -10 40, 0 40 C10 40, 15 30, 15 20 C15 10, 10 0, 0 0 Z" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <line x1="0" y1="-10" x2="0" y2="0" stroke="#8b2a2a" stroke-width="2"/>
      <line x1="0" y1="40" x2="0" y2="50" stroke="#8b2a2a" stroke-width="2"/>
    </g>
    
    <!-- 左下角装饰：灯笼 -->
    <g transform="translate(50, 250)">
      <path d="M0 0 C-10 0, -15 -10, -15 -20 C-15 -30, -10 -40, 0 -40 C10 -40, 15 -30, 15 -20 C15 -10, 10 0, 0 0 Z" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <line x1="0" y1="10" x2="0" y2="0" stroke="#8b2a2a" stroke-width="2"/>
      <line x1="0" y1="-40" x2="0" y2="-50" stroke="#8b2a2a" stroke-width="2"/>
    </g>
    
    <!-- 右下角装饰：灯笼 -->
    <g transform="translate(1150, 250)">
      <path d="M0 0 C-10 0, -15 -10, -15 -20 C-15 -30, -10 -40, 0 -40 C10 -40, 15 -30, 15 -20 C15 -10, 10 0, 0 0 Z" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <line x1="0" y1="10" x2="0" y2="0" stroke="#8b2a2a" stroke-width="2"/>
      <line x1="0" y1="-40" x2="0" y2="-50" stroke="#8b2a2a" stroke-width="2"/>
    </g>
  </g>
</svg>