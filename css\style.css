/* 公共样式 - 所有页面共用 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f5f0;
}

a {
    text-decoration: none;
    color: #8b2a2a;
    transition: color 0.3s ease;
}

a:hover {
    color: #d44949;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #8b2a2a;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #d44949;
}

.btn-big {
    padding: 10px 20px;
    font-size: 1.1em;
}

/* 头部样式 */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5%;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.8em;
    color: #8b2a2a;
    font-weight: bold;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    font-size: 1.1em;
    padding: 5px 10px;
    border-radius: 4px;
}

nav ul li a.active {
    background-color: #8b2a2a;
    color: #fff;
}

/* 轮播图样式 */
.banner {
    position: relative;
    height: 500px;
    overflow: hidden;
}

.slider {
    height: 100%;
    position: relative;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease;
}

.slide.current {
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide .content {
    position: absolute;
    bottom: 70px;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 20px;
    text-align: center;
}

.slide .content h2 {
    margin-bottom: 10px;
    font-size: 2em;
}

/* 页面横幅 */
.page-banner {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.page-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 20px;
    border-radius: 5px;
    width: 80%;
    max-width: 800px;
}

.page-title h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

/* 首页简介部分 */
.intro, .features, .video-section, .news {
    margin: 40px 0;
}

section h2 {
    text-align: center;
    font-size: 2em;
    margin-bottom: 30px;
    color: #8b2a2a;
    position: relative;
}

section h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #8b2a2a;
    margin: 10px auto;
}

.intro-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 30px;
}

.intro-text {
    flex: 1;
    min-width: 300px;
}

.intro-text p {
    margin-bottom: 15px;
    text-align: justify;
}

.intro-image {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.intro-image img {
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 特色卡片 */
.feature-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 300px;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-10px);
}

.card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card h3 {
    padding: 15px 15px 5px;
    font-size: 1.3em;
    color: #8b2a2a;
}

.card p {
    padding: 0 15px 15px;
    color: #666;
}

.card .btn {
    margin: 0 15px 15px;
}

/* 视频部分 */
.video-container {
    max-width: 800px;
    margin: 0 auto;
}

.video-container video {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 音频部分 */
.audio-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.audio-container audio {
    width: 100%;
    margin-bottom: 15px;
}

.audio-description {
    color: #666;
    font-style: italic;
}

/* 最新动态 */
.news-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.news-item {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    width: 350px;
}

.news-date {
    color: #8b2a2a;
    font-weight: bold;
    margin-bottom: 10px;
}

.news-item h3 {
    margin-bottom: 10px;
    font-size: 1.3em;
}

.news-item p {
    color: #666;
    margin-bottom: 15px;
}

.read-more {
    font-weight: bold;
}

/* 页脚样式 */
footer {
    background-color: #333;
    color: #fff;
    padding: 40px 0 0;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-section {
    flex: 1;
    min-width: 300px;
    margin-bottom: 30px;
}

.footer-section h3 {
    font-size: 1.3em;
    margin-bottom: 15px;
    position: relative;
}

.footer-section h3:after {
    content: '';
    display: block;
    width: 30px;
    height: 2px;
    background-color: #8b2a2a;
    margin-top: 5px;
}

.footer-section p {
    margin-bottom: 15px;
}

.contact span {
    display: block;
    margin-bottom: 10px;
}

.footer-section.links ul li {
    margin-bottom: 10px;
}

.text-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 4px;
}

textarea.text-input {
    height: 100px;
    resize: none;
}

.footer-bottom {
    background-color: #222;
    padding: 10px 0;
    text-align: center;
    font-size: 0.9em;
}

/* 历史页面样式 */
.timeline {
    margin: 40px 0;
}

.timeline-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.timeline-container::after {
    content: '';
    position: absolute;
    width: 4px;
    background-color: #8b2a2a;
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -2px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
    margin-bottom: 30px;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-dot {
    position: absolute;
    width: 20px;
    height: 20px;
    right: -10px;
    background-color: #8b2a2a;
    border-radius: 50%;
    z-index: 1;
    top: 15px;
}

.timeline-item:nth-child(even) .timeline-dot {
    left: -10px;
}

.timeline-date {
    position: absolute;
    width: 100px;
    text-align: center;
    padding: 5px 0;
    background-color: #8b2a2a;
    color: #fff;
    border-radius: 4px;
    top: 12px;
    right: -120px;
}

.timeline-item:nth-child(even) .timeline-date {
    left: -120px;
}

.timeline-content {
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.timeline-content h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
}

.timeline-content p {
    margin-bottom: 15px;
}

.timeline-content img {
    width: 100%;
    border-radius: 5px;
    margin-top: 10px;
}

.history-detail {
    margin: 40px 0;
}

.history-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.history-text {
    flex: 2;
    min-width: 300px;
}

.history-text h3 {
    margin: 20px 0 10px;
    color: #8b2a2a;
}

.history-text p {
    margin-bottom: 15px;
    text-align: justify;
}

.history-image {
    flex: 1;
    min-width: 300px;
}

.history-image img {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.image-caption {
    text-align: center;
    margin-top: 10px;
    color: #666;
    font-style: italic;
}

/* 分类页面样式 */
.category-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
}

.tabs {
    max-width: 1000px;
    margin: 0 auto;
}

.tab-header {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.tab-btn {
    padding: 10px 20px;
    background-color: #f0f0f0;
    cursor: pointer;
    border-radius: 4px 4px 0 0;
    margin: 0 5px;
    transition: background-color 0.3s ease;
}

.tab-btn.active {
    background-color: #8b2a2a;
    color: #fff;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.category-items {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.category-item {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.category-item img {
    width: 300px;
    height: 300px;
    object-fit: cover;
}

.category-info {
    flex: 1;
    padding: 20px;
    min-width: 300px;
}

.category-info h4 {
    font-size: 1.5em;
    margin-bottom: 10px;
    color: #8b2a2a;
}

.category-info p {
    margin-bottom: 15px;
}

.category-info ul {
    margin-left: 20px;
    list-style-type: disc;
}

.category-info ul li {
    margin-bottom: 5px;
}

/* 搭配页面样式 */
.color-theory {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 30px;
    margin-bottom: 30px;
}

.color-text {
    flex: 2;
    min-width: 300px;
}

.color-wheel {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.color-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.color-card {
    width: 250px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    padding-bottom: 15px;
}

.color-sample {
    height: 50px;
}

.color-card h4 {
    text-align: center;
    margin: 15px 0 10px;
}

.color-card p {
    padding: 0 15px;
    text-align: center;
    color: #666;
}

.accessory-types, .occasion-types {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.accessory-type, .occasion-type {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.accessory-type img, .occasion-type img {
    width: 300px;
    height: 300px;
    object-fit: cover;
}

.accessory-info, .occasion-info {
    flex: 1;
    padding: 20px;
    min-width: 300px;
}

.accessory-info h3, .occasion-info h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
}

.accessory-info p, .occasion-info p {
    margin-bottom: 15px;
}

.accessory-info ul, .occasion-info ul {
    margin-left: 20px;
    list-style-type: disc;
}

.accessory-info ul li, .occasion-info ul li {
    margin-bottom: 5px;
}

.tips-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.tip-card {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.tip-card h3 {
    margin-bottom: 15px;
    color: #8b2a2a;
}

.tip-card ul {
    margin-left: 20px;
    list-style-type: disc;
}

.tip-card ul li {
    margin-bottom: 10px;
}

.case-gallery {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.case-item {
    width: 280px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.case-item img {
    width: 100%;
    height: 350px;
    object-fit: cover;
}

.case-info {
    padding: 15px;
}

.case-info h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
}

.case-info p {
    color: #666;
}

/* 活动页面样式 */
.event-cards {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.event-card {
    display: flex;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.event-date {
    width: 100px;
    background-color: #8b2a2a;
    color: #fff;
    text-align: center;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.event-date .month {
    font-size: 1.2em;
}

.event-date .day {
    font-size: 2em;
    font-weight: bold;
    margin: 5px 0;
}

.event-info {
    flex: 1;
    padding: 20px;
}

.event-info h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
}

.event-meta {
    margin-bottom: 15px;
    color: #666;
}

.event-meta span {
    margin-right: 20px;
}

.community-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.community-card {
    width: 300px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.community-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.community-card h3 {
    padding: 15px 15px 5px;
    color: #8b2a2a;
}

.community-card p {
    padding: 0 15px 15px;
    color: #666;
}

.community-contact {
    padding: 0 15px 15px;
}

.community-contact span {
    display: block;
    margin-bottom: 5px;
}

.activity-gallery {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

.gallery-item {
    width: 280px;
    height: 280px;
    overflow: hidden;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.join-steps {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.step-card {
    width: 250px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
}

.step-number {
    width: 40px;
    height: 40px;
    background-color: #8b2a2a;
    color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 15px;
    font-size: 1.2em;
    font-weight: bold;
}

.step-card h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
}

.registration-form {
    max-width: 600px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

select.form-control {
    height: 40px;
}

textarea.form-control {
    height: 100px;
    resize: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        padding: 10px;
    }

    .logo {
        margin-bottom: 10px;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    nav ul li {
        margin: 5px;
    }

    .banner {
        height: 300px;
    }

    .page-banner {
        height: 200px;
    }

    .timeline-container::after {
        left: 31px;
    }

    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }

    .timeline-item:nth-child(even) {
        left: 0;
    }

    .timeline-dot {
        left: 21px;
        right: auto;
    }

    .timeline-date {
        width: auto;
        position: relative;
        right: auto;
        left: auto;
        top: auto;
        margin-bottom: 10px;
        display: inline-block;
    }

    .timeline-item:nth-child(even) .timeline-date {
        left: auto;
    }

    .event-card {
        flex-direction: column;
    }

    .event-date {
        width: 100%;
        padding: 10px 0;
    }
}

/* 购物页面样式 */
.gouwu-banner {
    width: 100%;
    height: 300px;
    background-color: #f8f4e9;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    overflow: hidden;
    position: relative;
}

.gouwu-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.shopping-guide {
    padding: 30px 0;
}

.shopping-tips {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 40px;
}

.tip-card {
    width: calc(33.33% - 20px);
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-5px);
}

.tip-card h4 {
    color: #8c4b3c;
    margin-bottom: 10px;
    font-family: 'KaiTi', serif;
}

.tip-card p {
    color: #666;
    line-height: 1.6;
}

.brand-recommendations {
    margin-bottom: 40px;
}

.brand-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.brand-card {
    width: calc(25% - 20px);
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
}

.brand-card:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.brand-card img {
    width: 100%;
    height: 150px;
    object-fit: contain;
    margin-bottom: 10px;
}

.brand-card h4 {
    color: #8c4b3c;
    margin-bottom: 5px;
    font-family: 'KaiTi', serif;
}

.brand-card p {
    color: #666;
    font-size: 14px;
}

.hot-products {
    margin-bottom: 40px;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
}

.filter-option {
    margin-right: 15px;
    margin-bottom: 10px;
}

.filter-option label {
    margin-right: 5px;
    color: #8c4b3c;
    font-weight: bold;
}

.filter-option select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
}

.product-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.product-card {
    width: calc(33.33% - 20px);
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 30px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.product-card img {
    width: 100%;
    height: 200px;
    object-fit: contain;
    margin-bottom: 15px;
    border-radius: 4px;
}

.product-card h4 {
    color: #8c4b3c;
    margin-bottom: 10px;
    font-family: 'KaiTi', serif;
}

.product-card .price {
    color: #e74c3c;
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 10px;
}

.product-card .description {
    color: #666;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
}

.product-card .btn {
    display: inline-block;
    background-color: #8c4b3c;
    color: #fff;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.product-card .btn:hover {
    background-color: #6d3a2e;
}

.shopping-guide-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 40px;
}

.guide-card {
    width: calc(50% - 20px);
    background-color: #f8f4e9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.guide-card h4 {
    color: #8c4b3c;
    margin-bottom: 15px;
    font-family: 'KaiTi', serif;
}

.guide-card ul {
    padding-left: 20px;
}

.guide-card li {
    margin-bottom: 10px;
    color: #555;
    line-height: 1.6;
}

.shopping-platforms {
    margin-bottom: 40px;
}

.platform-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.platform-card {
    width: calc(25% - 20px);
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
}

.platform-card:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.platform-card img {
    width: 100%;
    height: 120px;
    object-fit: contain;
    margin-bottom: 10px;
}

.platform-card h4 {
    color: #8c4b3c;
    margin-bottom: 5px;
    font-family: 'KaiTi', serif;
}

.platform-card p {
    color: #666;
    font-size: 14px;
}

/* 购物页面 - 响应式设计 */
@media (max-width: 768px) {
    .tip-card {
        width: 100%;
    }

    .brand-card {
        width: calc(50% - 15px);
    }

    .product-card {
        width: calc(50% - 15px);
    }

    .guide-card {
        width: 100%;
    }

    .platform-card {
        width: calc(50% - 15px);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 1s ease-in-out;
}

/* JavaScript 交互样式 */
.tab-btn {
    cursor: pointer;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}