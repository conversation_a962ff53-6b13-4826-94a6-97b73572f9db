<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="300" height="200" fill="#f0f5f9" rx="10" ry="10" />
  
  <!-- 品牌标志 -->
  <g transform="translate(150, 100)">
    <!-- 外圆 -->
    <circle cx="0" cy="0" r="70" fill="#e8f4f8" stroke="#7e57c2" stroke-width="2" />
    
    <!-- 内部图案 - 梨花 -->
    <g transform="translate(0, -10)">
      <!-- 花朵 -->
      <path d="M0,0 C-5,-5 -5,-15 0,-20 C5,-15 5,-5 0,0" fill="#f8bbd0" />
      <path d="M0,0 C5,-5 15,-5 20,0 C15,5 5,5 0,0" fill="#f8bbd0" />
      <path d="M0,0 C5,5 5,15 0,20 C-5,15 -5,5 0,0" fill="#f8bbd0" />
      <path d="M0,0 C-5,5 -15,5 -20,0 C-15,-5 -5,-5 0,0" fill="#f8bbd0" />
      
      <!-- 花蕊 -->
      <circle cx="0" cy="0" r="5" fill="#ffeb3b" />
    </g>
    
    <!-- 水波纹 - 渡 -->
    <path d="M-40,30 C-30,25 -20,25 -10,30 C0,35 10,35 20,30 C30,25 40,25 50,30" fill="none" stroke="#7e57c2" stroke-width="2" />
    <path d="M-50,40 C-40,35 -30,35 -20,40 C-10,45 0,45 10,40 C20,35 30,35 40,40" fill="none" stroke="#7e57c2" stroke-width="2" />
    
    <!-- 品牌名称 -->
    <text x="0" y="-45" font-family="'SimSun', serif" font-size="16" font-weight="bold" fill="#7e57c2" text-anchor="middle">梨花渡</text>
    <text x="0" y="60" font-family="'KaiTi', serif" font-size="12" fill="#7e57c2" text-anchor="middle">清新雅致的女性汉服</text>
  </g>
  
  <!-- 装饰元素 - 梨花枝 -->
  <g transform="translate(40, 40)">
    <path d="M0,0 C10,10 20,15 30,10" fill="none" stroke="#a1887f" stroke-width="1" />
    <circle cx="10" cy="5" r="3" fill="#f8bbd0" />
    <circle cx="20" cy="8" r="3" fill="#f8bbd0" />
    <circle cx="30" cy="10" r="3" fill="#f8bbd0" />
  </g>
  
  <g transform="translate(260, 40)">
    <path d="M0,0 C-10,10 -20,15 -30,10" fill="none" stroke="#a1887f" stroke-width="1" />
    <circle cx="-10" cy="5" r="3" fill="#f8bbd0" />
    <circle cx="-20" cy="8" r="3" fill="#f8bbd0" />
    <circle cx="-30" cy="10" r="3" fill="#f8bbd0" />
  </g>
  
  <g transform="translate(40, 160)">
    <path d="M0,0 C10,-10 20,-15 30,-10" fill="none" stroke="#a1887f" stroke-width="1" />
    <circle cx="10" cy="-5" r="3" fill="#f8bbd0" />
    <circle cx="20" cy="-8" r="3" fill="#f8bbd0" />
    <circle cx="30" cy="-10" r="3" fill="#f8bbd0" />
  </g>
  
  <g transform="translate(260, 160)">
    <path d="M0,0 C-10,-10 -20,-15 -30,-10" fill="none" stroke="#a1887f" stroke-width="1" />
    <circle cx="-10" cy="-5" r="3" fill="#f8bbd0" />
    <circle cx="-20" cy="-8" r="3" fill="#f8bbd0" />
    <circle cx="-30" cy="-10" r="3" fill="#f8bbd0" />
  </g>
  
  <!-- 底部装饰线 -->
  <path d="M70,180 L230,180" stroke="#7e57c2" stroke-width="1" stroke-dasharray="5,3" />
</svg>