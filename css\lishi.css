/* 历史页面专用样式 */

/* 时间线样式 */
.timeline {
    margin: 40px 0;
}

.timeline-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.timeline-container::after {
    content: '';
    position: absolute;
    width: 4px;
    background-color: #8b2a2a;
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -2px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
    margin-bottom: 30px;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-dot {
    position: absolute;
    width: 20px;
    height: 20px;
    right: -10px;
    background-color: #8b2a2a;
    border-radius: 50%;
    z-index: 1;
    top: 15px;
    border: 3px solid #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.timeline-item:nth-child(even) .timeline-dot {
    left: -10px;
}

.timeline-date {
    position: absolute;
    width: 120px;
    text-align: center;
    padding: 8px 0;
    background-color: #8b2a2a;
    color: #fff;
    border-radius: 4px;
    top: 12px;
    right: -140px;
    font-size: 0.9em;
    font-weight: bold;
}

.timeline-item:nth-child(even) .timeline-date {
    left: -140px;
}

.timeline-content {
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-5px);
}

.timeline-content h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
    font-size: 1.4em;
}

.timeline-content p {
    margin-bottom: 15px;
    line-height: 1.7;
    text-align: justify;
}

.timeline-content img {
    width: 100%;
    border-radius: 5px;
    margin-top: 10px;
}

/* 历史详解部分 */
.history-detail {
    margin: 40px 0;
}

.history-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    align-items: flex-start;
}

.history-text {
    flex: 2;
    min-width: 300px;
}

.history-text h3 {
    margin: 25px 0 15px;
    color: #8b2a2a;
    font-size: 1.4em;
    border-left: 4px solid #8b2a2a;
    padding-left: 15px;
}

.history-text p {
    margin-bottom: 15px;
    text-align: justify;
    line-height: 1.8;
    color: #555;
}

.history-image {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.history-image img {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.image-caption {
    text-align: center;
    margin-top: 10px;
    color: #666;
    font-style: italic;
    font-size: 0.9em;
}

/* 音频部分样式 */
.audio-section {
    margin: 40px 0;
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.audio-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.audio-container audio {
    width: 100%;
    margin-bottom: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .timeline-container::after {
        left: 31px;
    }
    
    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }
    
    .timeline-item:nth-child(even) {
        left: 0;
    }
    
    .timeline-dot {
        left: 21px;
        right: auto;
    }
    
    .timeline-date {
        width: auto;
        position: relative;
        right: auto;
        left: auto;
        top: auto;
        margin-bottom: 10px;
        display: inline-block;
    }
    
    .timeline-item:nth-child(even) .timeline-date {
        left: auto;
    }
    
    .history-content {
        flex-direction: column;
    }
    
    .history-text {
        min-width: auto;
    }
    
    .history-image {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .timeline-content {
        padding: 15px;
    }
    
    .timeline-content h3 {
        font-size: 1.2em;
    }
    
    .history-text h3 {
        font-size: 1.2em;
        margin: 20px 0 10px;
    }
}
