/* 历史页面样式 - 包含公共样式和历史页面专用样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f5f0;
}

a {
    text-decoration: none;
    color: #8b2a2a;
    transition: color 0.3s ease;
}

a:hover {
    color: #d44949;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #8b2a2a;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #d44949;
}

.btn-big {
    padding: 10px 20px;
    font-size: 1.1em;
}

/* 头部样式 */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5%;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.8em;
    color: #8b2a2a;
    font-weight: bold;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    font-size: 1.1em;
    padding: 5px 10px;
    border-radius: 4px;
}

nav ul li a.active {
    background-color: #8b2a2a;
    color: #fff;
}

/* 页面横幅通用样式 */
.page-banner {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.page-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 20px;
    border-radius: 5px;
    width: 80%;
    max-width: 800px;
}

.page-title h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

/* 通用章节标题样式 */
section h2 {
    text-align: center;
    font-size: 2em;
    margin-bottom: 30px;
    color: #8b2a2a;
    position: relative;
}

section h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #8b2a2a;
    margin: 10px auto;
}

/* 通用卡片样式 */
.card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* 视频和音频容器通用样式 */
.video-container, .audio-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.video-container video, .audio-container audio {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.audio-description {
    color: #666;
    font-style: italic;
    margin-top: 15px;
}

/* 页脚样式 */
footer {
    background-color: #333;
    color: #fff;
    padding: 40px 0 0;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-section {
    flex: 1;
    min-width: 300px;
    margin-bottom: 30px;
}

.footer-section h3 {
    font-size: 1.3em;
    margin-bottom: 15px;
    position: relative;
}

.footer-section h3:after {
    content: '';
    display: block;
    width: 30px;
    height: 2px;
    background-color: #8b2a2a;
    margin-top: 5px;
}

.footer-section p {
    margin-bottom: 15px;
}

.contact span {
    display: block;
    margin-bottom: 10px;
}

.footer-section.links ul li {
    margin-bottom: 10px;
}

.text-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 4px;
}

textarea.text-input {
    height: 100px;
    resize: none;
}

.footer-bottom {
    background-color: #222;
    padding: 10px 0;
    text-align: center;
    font-size: 0.9em;
}

/* 历史页面专用样式 */

/* 时间线样式 */
.timeline {
    margin: 40px 0;
}

.timeline-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.timeline-container::after {
    content: '';
    position: absolute;
    width: 4px;
    background-color: #8b2a2a;
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -2px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
    margin-bottom: 30px;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-dot {
    position: absolute;
    width: 20px;
    height: 20px;
    right: -10px;
    background-color: #8b2a2a;
    border-radius: 50%;
    z-index: 1;
    top: 15px;
    border: 3px solid #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.timeline-item:nth-child(even) .timeline-dot {
    left: -10px;
}

.timeline-date {
    position: absolute;
    width: 120px;
    text-align: center;
    padding: 8px 0;
    background-color: #8b2a2a;
    color: #fff;
    border-radius: 4px;
    top: 12px;
    right: -140px;
    font-size: 0.9em;
    font-weight: bold;
}

.timeline-item:nth-child(even) .timeline-date {
    left: -140px;
}

.timeline-content {
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-5px);
}

.timeline-content h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
    font-size: 1.4em;
}

.timeline-content p {
    margin-bottom: 15px;
    line-height: 1.7;
    text-align: justify;
}

.timeline-content img {
    width: 100%;
    border-radius: 5px;
    margin-top: 10px;
}

/* 历史详解部分 */
.history-detail {
    margin: 40px 0;
}

.history-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    align-items: flex-start;
}

.history-text {
    flex: 2;
    min-width: 300px;
}

.history-text h3 {
    margin: 25px 0 15px;
    color: #8b2a2a;
    font-size: 1.4em;
    border-left: 4px solid #8b2a2a;
    padding-left: 15px;
}

.history-text p {
    margin-bottom: 15px;
    text-align: justify;
    line-height: 1.8;
    color: #555;
}

.history-image {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.history-image img {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.image-caption {
    text-align: center;
    margin-top: 10px;
    color: #666;
    font-style: italic;
    font-size: 0.9em;
}

/* 音频部分样式 */
.audio-section {
    margin: 40px 0;
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.audio-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.audio-container audio {
    width: 100%;
    margin-bottom: 15px;
}

/* 响应式设计 - 公共响应式样式 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        padding: 10px;
    }

    .logo {
        margin-bottom: 10px;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    nav ul li {
        margin: 5px;
    }

    .page-banner {
        height: 200px;
    }

    .page-title h1 {
        font-size: 1.8em;
    }

    .footer-content {
        flex-direction: column;
    }

    .footer-section {
        min-width: auto;
    }

    /* 历史页面响应式样式 */
    .timeline-container::after {
        left: 31px;
    }

    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }

    .timeline-item:nth-child(even) {
        left: 0;
    }

    .timeline-dot {
        left: 21px;
        right: auto;
    }

    .timeline-date {
        width: auto;
        position: relative;
        right: auto;
        left: auto;
        top: auto;
        margin-bottom: 10px;
        display: inline-block;
    }

    .timeline-item:nth-child(even) .timeline-date {
        left: auto;
    }

    .history-content {
        flex-direction: column;
    }

    .history-text {
        min-width: auto;
    }

    .history-image {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .timeline-content {
        padding: 15px;
    }

    .timeline-content h3 {
        font-size: 1.2em;
    }

    .history-text h3 {
        font-size: 1.2em;
        margin: 20px 0 10px;
    }
}
