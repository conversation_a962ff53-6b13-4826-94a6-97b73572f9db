<svg xmlns="http://www.w3.org/2000/svg" width="400" height="250" viewBox="0 0 400 250">
  <!-- 背景 -->
  <rect width="400" height="250" fill="#f5e8d6" rx="5" ry="5"/>
  
  <!-- 装饰边框 -->
  <rect x="5" y="5" width="390" height="240" fill="none" stroke="#8b2a2a" stroke-width="1" rx="3" ry="3"/>
  
  <!-- 中央图案：多种汉服款式展示 -->
  <g transform="translate(200, 125)">
    <!-- 汉服款式1：齐胸襦裙 -->
    <g transform="translate(-120, 0)">
      <!-- 人物轮廓 -->
      <path d="M0 -60 C-5 -60, -10 -55, -10 -50 L-10 -20 L-15 0 L-10 20 L-5 30 L0 35 L5 30 L10 20 L15 0 L10 -20 L10 -50 C10 -55, 5 -60, 0 -60 Z" fill="#8b2a2a"/>
      
      <!-- 发髻 -->
      <path d="M0 -60 C-3 -65, -1 -68, 0 -70 C1 -68, 3 -65, 0 -60 Z" fill="#8b2a2a"/>
      
      <!-- 标签 -->
      <text x="0" y="50" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#8b2a2a">齐胸襦裙</text>
    </g>
    
    <!-- 汉服款式2：交领襦裙 -->
    <g transform="translate(-40, 0)">
      <!-- 人物轮廓 -->
      <path d="M0 -60 C-5 -60, -10 -55, -10 -50 L-10 -20 L-15 0 L-10 20 L-5 30 L0 35 L5 30 L10 20 L15 0 L10 -20 L10 -50 C10 -55, 5 -60, 0 -60 Z" fill="#8b2a2a"/>
      
      <!-- 交领 -->
      <path d="M0 -40 L-5 -30 L0 -20 L5 -30 Z" fill="#f5e8d6" stroke="#8b2a2a" stroke-width="0.5"/>
      
      <!-- 发髻 -->
      <path d="M0 -60 C-3 -65, -1 -68, 0 -70 C1 -68, 3 -65, 0 -60 Z" fill="#8b2a2a"/>
      
      <!-- 标签 -->
      <text x="0" y="50" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#8b2a2a">交领襦裙</text>
    </g>
    
    <!-- 汉服款式3：直裾深衣 -->
    <g transform="translate(40, 0)">
      <!-- 人物轮廓 -->
      <path d="M0 -60 C-5 -60, -10 -55, -10 -50 L-10 -20 L-15 0 L-10 20 L-5 30 L0 35 L5 30 L10 20 L15 0 L10 -20 L10 -50 C10 -55, 5 -60, 0 -60 Z" fill="#8b2a2a"/>
      
      <!-- 直裾 -->
      <path d="M-10 -20 L-10 20 M10 -20 L10 20" stroke="#f5e8d6" stroke-width="0.5"/>
      
      <!-- 发髻 -->
      <path d="M0 -60 C-3 -65, -1 -68, 0 -70 C1 -68, 3 -65, 0 -60 Z" fill="#8b2a2a"/>
      
      <!-- 标签 -->
      <text x="0" y="50" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#8b2a2a">直裾深衣</text>
    </g>
    
    <!-- 汉服款式4：袄裙 -->
    <g transform="translate(120, 0)">
      <!-- 人物轮廓 -->
      <path d="M0 -60 C-5 -60, -10 -55, -10 -50 L-10 -20 L-15 0 L-10 20 L-5 30 L0 35 L5 30 L10 20 L15 0 L10 -20 L10 -50 C10 -55, 5 -60, 0 -60 Z" fill="#8b2a2a"/>
      
      <!-- 袄裙特征 -->
      <path d="M-10 -10 L10 -10" stroke="#f5e8d6" stroke-width="0.5"/>
      
      <!-- 发髻 -->
      <path d="M0 -60 C-3 -65, -1 -68, 0 -70 C1 -68, 3 -65, 0 -60 Z" fill="#8b2a2a"/>
      
      <!-- 标签 -->
      <text x="0" y="50" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#8b2a2a">袄裙</text>
    </g>
  </g>
  
  <!-- 标题 -->
  <g transform="translate(200, 220)">
    <rect x="-100" y="-20" width="200" height="40" fill="rgba(139, 42, 42, 0.8)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">多样款式</text>
  </g>
  
  <!-- 装饰花纹 -->
  <g fill="#8b2a2a" opacity="0.2">
    <path d="M20 20 C30 10, 40 10, 50 20 C60 10, 70 10, 80 20" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    <path d="M320 20 C330 10, 340 10, 350 20 C360 10, 370 10, 380 20" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    <path d="M20 230 C30 240, 40 240, 50 230 C60 240, 70 240, 80 230" stroke="#8b2a2a" stroke-width="1" fill="none"/>
    <path d="M320 230 C330 240, 340 240, 350 230 C360 240, 370 240, 380 230" stroke="#8b2a2a" stroke-width="1" fill="none"/>
  </g>
</svg>