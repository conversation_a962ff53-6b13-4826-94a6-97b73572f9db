<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="300" viewBox="0 0 1200 300">
  <!-- 背景 -->
  <rect width="1200" height="300" fill="#f9eee2"/>
  
  <!-- 装饰边框 -->
  <rect x="10" y="10" width="1180" height="280" fill="none" stroke="#8b2a2a" stroke-width="2" rx="5" ry="5" stroke-dasharray="10,5"/>
  
  <!-- 中央图案：汉服搭配展示 -->
  <g transform="translate(600, 150)">
    <!-- 中央人物 -->
    <g transform="translate(0, 0)">
      <!-- 人物轮廓 -->
      <path d="M0 -80 C-15 -80, -30 -65, -30 -50 L-30 0 L-40 50 L-30 70 L-15 90 L0 100 L15 90 L30 70 L40 50 L30 0 L30 -50 C30 -65, 15 -80, 0 -80 Z" fill="#8b2a2a"/>
      
      <!-- 发髻 -->
      <path d="M0 -80 C-5 -90, -2 -95, 0 -100 C2 -95, 5 -90, 0 -80 Z" fill="#8b2a2a"/>
    </g>
    
    <!-- 左侧配饰元素 -->
    <g transform="translate(-200, 0)">
      <!-- 发簪 -->
      <g transform="translate(0, -60)">
        <rect x="-20" y="-5" width="40" height="10" fill="#8b2a2a" rx="2" ry="2"/>
        <path d="M-15 -5 L-10 -15 L10 -15 L15 -5" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">发簪</text>
      </g>
      
      <!-- 耳环 -->
      <g transform="translate(0, 0)">
        <circle cx="0" cy="0" r="10" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <path d="M0 -10 L0 -20" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">耳环</text>
      </g>
      
      <!-- 腰带 -->
      <g transform="translate(0, 60)">
        <rect x="-25" y="-5" width="50" height="10" fill="#8b2a2a" rx="2" ry="2"/>
        <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">腰带</text>
      </g>
    </g>
    
    <!-- 右侧色彩元素 -->
    <g transform="translate(200, 0)">
      <!-- 色彩圆环 -->
      <g transform="translate(0, -60)">
        <circle cx="0" cy="0" r="20" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <path d="M0 0 L0 -20" stroke="#8b2a2a" stroke-width="1" transform="rotate(0)"/>
        <path d="M0 0 L0 -20" stroke="#8b2a2a" stroke-width="1" transform="rotate(60)"/>
        <path d="M0 0 L0 -20" stroke="#8b2a2a" stroke-width="1" transform="rotate(120)"/>
        <path d="M0 0 L0 -20" stroke="#8b2a2a" stroke-width="1" transform="rotate(180)"/>
        <path d="M0 0 L0 -20" stroke="#8b2a2a" stroke-width="1" transform="rotate(240)"/>
        <path d="M0 0 L0 -20" stroke="#8b2a2a" stroke-width="1" transform="rotate(300)"/>
        <text x="0" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">色彩搭配</text>
      </g>
      
      <!-- 场合图标 -->
      <g transform="translate(0, 0)">
        <rect x="-15" y="-15" width="30" height="30" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#8b2a2a">场合</text>
        <text x="0" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">场合搭配</text>
      </g>
      
      <!-- 季节图标 -->
      <g transform="translate(0, 60)">
        <circle cx="-10" cy="-10" r="5" fill="#8b2a2a"/>
        <circle cx="10" cy="-10" r="5" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <circle cx="-10" cy="10" r="5" fill="none" stroke="#8b2a2a" stroke-width="1"/>
        <circle cx="10" cy="10" r="5" fill="#8b2a2a"/>
        <text x="0" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">季节搭配</text>
      </g>
    </g>
    
    <!-- 连接线 -->
    <line x1="-100" y1="-60" x2="-30" y2="-60" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    <line x1="-100" y1="0" x2="-40" y2="0" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    <line x1="-100" y1="60" x2="-40" y2="50" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    
    <line x1="100" y1="-60" x2="30" y2="-60" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    <line x1="100" y1="0" x2="40" y2="0" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
    <line x1="100" y1="60" x2="40" y2="50" stroke="#8b2a2a" stroke-width="1" stroke-dasharray="5,5"/>
  </g>
  
  <!-- 标题 -->
  <g transform="translate(600, 50)">
    <rect x="-200" y="-30" width="400" height="60" fill="rgba(139, 42, 42, 0.8)" rx="10" ry="10"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="#fff">汉服搭配艺术</text>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.2">
    <!-- 左上角装饰 -->
    <g transform="translate(50, 50)">
      <circle cx="0" cy="0" r="20" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <circle cx="0" cy="0" r="10" fill="none" stroke="#8b2a2a" stroke-width="1"/>
    </g>
    
    <!-- 右上角装饰 -->
    <g transform="translate(1150, 50)">
      <circle cx="0" cy="0" r="20" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <circle cx="0" cy="0" r="10" fill="none" stroke="#8b2a2a" stroke-width="1"/>
    </g>
    
    <!-- 左下角装饰 -->
    <g transform="translate(50, 250)">
      <circle cx="0" cy="0" r="20" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <circle cx="0" cy="0" r="10" fill="none" stroke="#8b2a2a" stroke-width="1"/>
    </g>
    
    <!-- 右下角装饰 -->
    <g transform="translate(1150, 250)">
      <circle cx="0" cy="0" r="20" fill="none" stroke="#8b2a2a" stroke-width="2"/>
      <circle cx="0" cy="0" r="10" fill="none" stroke="#8b2a2a" stroke-width="1"/>
    </g>
  </g>
</svg>