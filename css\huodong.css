/* 活动页面专用样式 */

/* 活动介绍部分 */
.activity-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.activity-intro p {
    line-height: 1.8;
    color: #555;
    font-size: 1.1em;
}

/* 近期活动部分 */
.upcoming-events {
    margin: 50px 0;
}

.event-cards {
    display: flex;
    flex-direction: column;
    gap: 25px;
    max-width: 900px;
    margin: 0 auto;
}

.event-card {
    display: flex;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.event-date {
    width: 120px;
    background-color: #8b2a2a;
    color: #fff;
    text-align: center;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-shrink: 0;
}

.event-date .month {
    font-size: 1em;
    margin-bottom: 5px;
}

.event-date .day {
    font-size: 2.5em;
    font-weight: bold;
    margin: 5px 0;
}

.event-date .year {
    font-size: 0.9em;
    margin-top: 5px;
}

.event-info {
    flex: 1;
    padding: 25px;
}

.event-info h3 {
    margin-bottom: 15px;
    color: #8b2a2a;
    font-size: 1.5em;
}

.event-meta {
    margin-bottom: 15px;
    color: #666;
    font-size: 0.95em;
}

.event-meta span {
    margin-right: 20px;
    display: inline-block;
}

.event-info p {
    color: #555;
    line-height: 1.7;
    margin-bottom: 20px;
    text-align: justify;
}

/* 活动类型部分 */
.activity-types {
    margin: 50px 0;
}

.activity-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.activity-item {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.activity-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.activity-item img {
    width: 300px;
    height: 300px;
    object-fit: cover;
    flex-shrink: 0;
}

.activity-info {
    flex: 1;
    padding: 25px;
    min-width: 300px;
}

.activity-info h3 {
    font-size: 1.6em;
    margin-bottom: 15px;
    color: #8b2a2a;
    border-bottom: 2px solid #8b2a2a;
    padding-bottom: 5px;
}

.activity-info p {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #555;
    text-align: justify;
}

.activity-info h4 {
    color: #8b2a2a;
    margin: 20px 0 10px;
    font-size: 1.2em;
}

.activity-info ul {
    margin-left: 0;
    list-style: none;
    margin-bottom: 15px;
}

.activity-info ul li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
    color: #666;
    line-height: 1.6;
}

.activity-info ul li:before {
    content: "•";
    color: #8b2a2a;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* 汉服社团部分 */
.community-groups {
    margin: 50px 0;
}

.community-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.community-intro p {
    line-height: 1.8;
    color: #555;
    font-size: 1.1em;
}

.community-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}

.community-card {
    width: 280px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.community-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.community-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.community-card h3 {
    padding: 15px 15px 10px;
    color: #8b2a2a;
    font-size: 1.2em;
}

.community-card p {
    padding: 0 15px 15px;
    color: #666;
    line-height: 1.6;
    font-size: 0.95em;
}

.community-contact {
    padding: 0 15px 20px;
}

.community-contact span {
    display: block;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.community-contact a {
    color: #8b2a2a;
    text-decoration: none;
}

.community-contact a:hover {
    color: #d44949;
}

/* 活动掠影部分 */
.activity-gallery {
    margin: 50px 0;
}

.gallery-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.gallery-item {
    width: 280px;
    height: 280px;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: #fff;
    padding: 20px 15px 15px;
    font-size: 0.9em;
    text-align: center;
}

/* 参与步骤部分 */
.join-activity {
    margin: 50px 0;
}

.join-steps {
    display: flex;
    flex-direction: column;
    gap: 25px;
    max-width: 800px;
    margin: 0 auto;
}

.step {
    display: flex;
    align-items: flex-start;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 25px;
    transition: transform 0.3s ease;
}

.step:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.step-number {
    width: 50px;
    height: 50px;
    background-color: #8b2a2a;
    color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5em;
    font-weight: bold;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content h3 {
    margin-bottom: 10px;
    color: #8b2a2a;
    font-size: 1.3em;
}

.step-content p {
    color: #666;
    line-height: 1.7;
}

/* 活动报名表单 */
.activity-form {
    margin: 50px 0;
}

.form-container {
    max-width: 600px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #8b2a2a;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group button {
    width: 100%;
    padding: 15px;
    background-color: #8b2a2a;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.form-group button:hover {
    background-color: #d44949;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .event-card {
        flex-direction: column;
    }

    .event-date {
        width: 100%;
        padding: 15px 0;
        flex-direction: row;
        justify-content: center;
        gap: 15px;
    }

    .event-date .day {
        font-size: 2em;
        margin: 0;
    }

    .event-info {
        padding: 20px;
    }

    .activity-item {
        flex-direction: column;
    }

    .activity-item img {
        width: 100%;
        height: 250px;
    }

    .activity-info {
        min-width: auto;
        padding: 20px;
    }

    .community-cards {
        flex-direction: column;
        align-items: center;
    }

    .community-card {
        width: 100%;
        max-width: 400px;
    }

    .gallery-container {
        flex-direction: column;
        align-items: center;
    }

    .gallery-item {
        width: 100%;
        max-width: 400px;
        height: 250px;
    }

    .step {
        flex-direction: column;
        text-align: center;
    }

    .step-number {
        margin: 0 auto 15px;
    }
}

@media (max-width: 480px) {
    .activity-intro, .community-intro {
        margin: 0 10px 30px;
        padding: 15px;
    }

    .event-info h3 {
        font-size: 1.2em;
    }

    .activity-info h3 {
        font-size: 1.3em;
    }

    .form-container {
        margin: 0 10px;
        padding: 20px;
    }

    .step {
        padding: 20px;
    }

    .step-number {
        width: 40px;
        height: 40px;
        font-size: 1.2em;
    }
}
