<svg xmlns="http://www.w3.org/2000/svg" width="800" height="500" viewBox="0 0 800 500">
  <!-- 背景 -->
  <rect width="800" height="500" fill="#f9f5f0" rx="10" ry="10"/>
  
  <!-- 装饰边框 -->
  <rect x="10" y="10" width="780" height="480" fill="none" stroke="#8b2a2a" stroke-width="2" rx="8" ry="8" stroke-dasharray="5,5"/>
  
  <!-- 标题 -->
  <g transform="translate(400, 50)">
    <rect x="-150" y="-25" width="300" height="50" fill="rgba(139, 42, 42, 0.8)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#fff">汉服分类总览</text>
  </g>
  
  <!-- 朝代分类 -->
  <g transform="translate(200, 150)">
    <rect x="-150" y="-20" width="300" height="40" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">按朝代分类</text>
    
    <!-- 朝代列表 -->
    <g transform="translate(0, 50)">
      <!-- 汉朝 -->
      <g transform="translate(-100, 0)">
        <circle cx="0" cy="0" r="30" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">汉朝</text>
      </g>
      
      <!-- 唐朝 -->
      <g transform="translate(-30, 0)">
        <circle cx="0" cy="0" r="30" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">唐朝</text>
      </g>
      
      <!-- 宋朝 -->
      <g transform="translate(40, 0)">
        <circle cx="0" cy="0" r="30" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">宋朝</text>
      </g>
      
      <!-- 明朝 -->
      <g transform="translate(110, 0)">
        <circle cx="0" cy="0" r="30" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">明朝</text>
      </g>
    </g>
    
    <!-- 朝代特征 -->
    <g transform="translate(0, 100)">
      <rect x="-140" y="-20" width="280" height="40" fill="rgba(139, 42, 42, 0.1)" rx="5" ry="5"/>
      <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">不同朝代的汉服具有不同的特征和风格</text>
    </g>
  </g>
  
  <!-- 性别分类 -->
  <g transform="translate(600, 150)">
    <rect x="-150" y="-20" width="300" height="40" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">按性别分类</text>
    
    <!-- 性别列表 -->
    <g transform="translate(0, 50)">
      <!-- 男装 -->
      <g transform="translate(-70, 0)">
        <circle cx="0" cy="0" r="40" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" text-anchor="middle" fill="#8b2a2a">男装</text>
        <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#8b2a2a">袍服、深衣</text>
      </g>
      
      <!-- 女装 -->
      <g transform="translate(70, 0)">
        <circle cx="0" cy="0" r="40" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" text-anchor="middle" fill="#8b2a2a">女装</text>
        <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" text-anchor="middle" fill="#8b2a2a">襦裙、褙子</text>
      </g>
    </g>
    
    <!-- 性别特征 -->
    <g transform="translate(0, 100)">
      <rect x="-140" y="-20" width="280" height="40" fill="rgba(139, 42, 42, 0.1)" rx="5" ry="5"/>
      <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" text-anchor="middle" fill="#8b2a2a">男女汉服在款式、色彩和装饰上有明显区别</text>
    </g>
  </g>
  
  <!-- 场合分类 -->
  <g transform="translate(200, 350)">
    <rect x="-150" y="-20" width="300" height="40" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">按场合分类</text>
    
    <!-- 场合列表 -->
    <g transform="translate(0, 50)">
      <!-- 日常 -->
      <g transform="translate(-100, 0)">
        <rect x="-40" y="-20" width="80" height="40" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">日常</text>
      </g>
      
      <!-- 礼服 -->
      <g transform="translate(0, 0)">
        <rect x="-40" y="-20" width="80" height="40" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">礼服</text>
      </g>
      
      <!-- 婚礼 -->
      <g transform="translate(100, 0)">
        <rect x="-40" y="-20" width="80" height="40" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">婚礼</text>
      </g>
    </g>
  </g>
  
  <!-- 结构分类 -->
  <g transform="translate(600, 350)">
    <rect x="-150" y="-20" width="300" height="40" fill="rgba(139, 42, 42, 0.6)" rx="5" ry="5"/>
    <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">按结构分类</text>
    
    <!-- 结构列表 -->
    <g transform="translate(0, 50)">
      <!-- 上衣下裳 -->
      <g transform="translate(-70, 0)">
        <rect x="-60" y="-20" width="120" height="40" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">上衣下裳</text>
      </g>
      
      <!-- 连体式 -->
      <g transform="translate(70, 0)">
        <rect x="-60" y="-20" width="120" height="40" fill="rgba(139, 42, 42, 0.1)" stroke="#8b2a2a" stroke-width="1" rx="5" ry="5"/>
        <text x="0" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" text-anchor="middle" fill="#8b2a2a">连体式</text>
      </g>
    </g>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.2">
    <!-- 左上角装饰 -->
    <g transform="translate(50, 50)">
      <path d="M0 0 C10 10, 20 10, 30 0 C40 10, 50 10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右上角装饰 -->
    <g transform="translate(750, 50)">
      <path d="M0 0 C-10 10, -20 10, -30 0 C-40 10, -50 10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 左下角装饰 -->
    <g transform="translate(50, 450)">
      <path d="M0 0 C10 -10, 20 -10, 30 0 C40 -10, 50 -10, 60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右下角装饰 -->
    <g transform="translate(750, 450)">
      <path d="M0 0 C-10 -10, -20 -10, -30 0 C-40 -10, -50 -10, -60 0" stroke="#8b2a2a" stroke-width="2" fill="none"/>
    </g>
  </g>
</svg>