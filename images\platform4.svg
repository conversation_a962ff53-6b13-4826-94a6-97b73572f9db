<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="200" height="200" fill="#f0f9ff" rx="10" ry="10" />
  
  <!-- 微信小程序标志 -->
  <g transform="translate(100, 100)">
    <!-- 外圆 -->
    <circle cx="0" cy="0" r="70" fill="#07c160" />
    
    <!-- 小程序图标 -->
    <g transform="translate(0, 0)">
      <!-- 白色方块 -->
      <rect x="-35" y="-35" width="70" height="70" rx="10" ry="10" fill="white" />
      
      <!-- 绿色小方块 -->
      <rect x="-25" y="-25" width="20" height="20" rx="5" ry="5" fill="#07c160" />
      <rect x="5" y="-25" width="20" height="20" rx="5" ry="5" fill="#07c160" />
      <rect x="-25" y="5" width="20" height="20" rx="5" ry="5" fill="#07c160" />
      <rect x="5" y="5" width="20" height="20" rx="5" ry="5" fill="#07c160" opacity="0.5" />
    </g>
  </g>
  
  <!-- 文字 -->
  <g transform="translate(100, 170)">
    <text x="0" y="0" font-family="'SimHei', sans-serif" font-size="16" font-weight="bold" fill="#07c160" text-anchor="middle">微信小程序</text>
  </g>
  
  <!-- 装饰元素 - 微信图标 -->
  <g transform="translate(30, 30)">
    <circle cx="0" cy="0" r="15" fill="#07c160" />
    <g transform="scale(0.5)">
      <!-- 微信人形图标 -->
      <path d="M-15,-5 C-15,-12 -5,-18 5,-18 C15,-18 25,-12 25,-5 C25,2 15,8 5,8 C0,8 -5,6 -8,4 L-15,8 L-12,0 C-14,-2 -15,-3 -15,-5 Z" fill="white" />
      <circle cx="-5" cy="-8" r="2" fill="#07c160" />
      <circle cx="10" cy="-8" r="2" fill="#07c160" />
    </g>
  </g>
  
  <!-- 装饰元素 - 扫码图标 -->
  <g transform="translate(170, 30)">
    <rect x="-10" y="-10" width="20" height="20" fill="white" stroke="#07c160" stroke-width="1" />
    <rect x="-6" y="-6" width="12" height="12" fill="#07c160" />
    <rect x="-3" y="-3" width="6" height="6" fill="white" />
  </g>
  
  <!-- 装饰元素 - 小程序云 -->
  <g transform="translate(30, 170)">
    <path d="M-10,-5 C-15,-10 -5,-15 0,-10 C5,-15 15,-10 10,-5 C15,0 10,5 5,5 C0,10 -10,5 -5,0 C-15,0 -15,-5 -10,-5 Z" fill="white" stroke="#07c160" stroke-width="1" />
  </g>
  
  <!-- 装饰元素 - 支付图标 -->
  <g transform="translate(170, 170)">
    <circle cx="0" cy="0" r="15" fill="white" stroke="#07c160" stroke-width="1" />
    <path d="M-5,-5 L-5,5 L5,0 Z" fill="#07c160" />
  </g>
</svg>